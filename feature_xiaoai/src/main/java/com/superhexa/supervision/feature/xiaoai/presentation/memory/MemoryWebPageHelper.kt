package com.superhexa.supervision.feature.xiaoai.presentation.memory

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import com.fasterxml.jackson.databind.ObjectMapper
import com.superhexa.supervision.feature.xiaoai.track.EventTrackHelper
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.superhexainterfaces.login.ILoginModuleApi
import com.tencent.mmkv.MMKV
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.utils.CommonUtils
import com.xiaomi.aivs.utils.SpeechEngineHelper
import org.json.JSONException
import org.json.JSONObject
import timber.log.Timber

class MemoryWebPageHelper(private val webView: WebView, private val context: Context, private val url: String?) {

    companion object {
        private const val TAG = "MemoryWebPageHelper"
        //全局toast
        private var currentToast: Toast? = null
        //最后一次toast文本
        private var lastToastText: String = ""
        private var lastToastTime: Long = 0L          // 新增：上次弹出的时间
        private const val TOAST_DEBOUNCE_MS = 2000L // 2 秒内相同文案不重复弹
        private const val ENV_DOMAIN = "env_domain_new"
        const val ENV_PRODUCTION: Int = 0
        const val ENV_PREVIEW: Int = 1
        const val ENV_STAGING: Int = 2
        const val ENV_PREVIEW4TEST: Int = 3
    }

    // 初始化小爱记忆web界面
    @SuppressLint("SetJavaScriptEnabled")
    fun initWebView(listener: MemoryWebInterfaceListener) {
        Timber.tag(TAG).d("url =$url")
        val webSettings = webView.settings
        webSettings.javaScriptEnabled = true

        webView.addJavascriptInterface(MemoryWebAppInterface(listener), "aiGlassesjsBridge")
        if (url != null) {
            webView.loadUrl(url)
        } else {
            webView.loadUrl(getHostUrl())
        }
    }

    private fun getHostUrl(): String {
        val env = MMKV.defaultMMKV().decodeInt(ENV_DOMAIN, 0)
        val url = when (env) {
            ENV_PREVIEW4TEST ->
                "https://i-preview4test.xiaomixiaoai.com/h5/ai-web-bussiness/ai-web-bussiness/memory-glasses/"
            ENV_PREVIEW ->
                "https://i-preview.xiaomixiaoai.com/h5/ai-web-bussiness/ai-web-bussiness/memory-glasses/"
            else -> { "https://i-preview.xiaomixiaoai.com/h5/ai-web-bussiness/ai-web-bussiness/memory-glasses/" }
        }
        return url
    }

    private fun wrapToJsonObj(input: String?): JSONObject {
        val jsonObject = JSONObject()
        jsonObject.put("data", input ?: "")
        return jsonObject
    }

    private fun executeJsCallback(webView: WebView, jsonString: String, data: String) {
        val jsonObj = JSONObject(jsonString)
        val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name

        webView.post {
            val jsonObject = wrapToJsonObj(data).toString()
            val jsCallback = "$onSuccess($jsonObject)"
            webView.evaluateJavascript(jsCallback) { result ->
                Timber.tag(TAG).d("executeJsCallback reult = $result")
            }
        }
    }

    fun refreshUserInfo() {
        ILoginModuleApi::class.java.impl.updateWearServiceToken()
    }

    fun getLocalUserInfo(): JSONObject {
        val userInfo = JSONObject()
        try {
            val authorization = AiSpeechEngine.INSTANCE.getAuthorization()

            val serviceToken = AccountManager.getMiWearServiceToken()
            val userId = AccountManager.getUserID()
            val deviceId = SpeechEngineHelper.getDeviceId(context)
            val userAgent = CommonUtils.getUserAgent(context)

            userInfo.put("serviceToken", serviceToken)
            userInfo.put("userId", userId)
            userInfo.put("deviceId", deviceId)
            userInfo.put("authorization", authorization)
            userInfo.put("userAgent", userAgent)

            Timber.tag(TAG).d("userInfo:$userInfo")
        } catch (e: JSONException) {
            Timber.tag(TAG).d("getLocalUserInfo() error: ${e.message}")
        }
        return userInfo
    }

    inner class MemoryWebAppInterface(private val listener: MemoryWebInterfaceListener) {
        @JavascriptInterface
        fun getUserInfo(jsonString: String) {
            val userInfo = getLocalUserInfo()
            Timber.tag(TAG).d("getUserInfo userInfo: $userInfo")
            executeJsCallback(webView, jsonString, userInfo.toString())
        }

        @JavascriptInterface
        fun refreshUserInfo(jsonString: String) {
            Timber.tag(TAG).d("refreshUserInfo() called")
            refreshUserInfo()
            val userInfo = getLocalUserInfo()
            Timber.tag(TAG).d("refreshUserInfo userInfo: $userInfo")
            executeJsCallback(webView, jsonString, userInfo.toString())
        }

        @JavascriptInterface
        fun getAppVersion(jsonString: String) {
            var version = -1
            try {
                val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                version = packageInfo.versionCode
                Timber.tag(TAG).d("version:$version")
            } catch (e: PackageManager.NameNotFoundException) {
                Timber.tag(TAG).d("onContentLoadFinish() error: ${e.message}")
            }
            executeJsCallback(webView, jsonString, version.toString())
        }

        @JavascriptInterface
        fun report(jsonObject: JSONObject) {
            Timber.tag(TAG).d("report() called")
            val type = jsonObject.getString("type")
            val params = jsonObject.getString("params")

            val objectMapper = ObjectMapper()
            val mapParams: Map<String, Any> = objectMapper.readValue(params, Map::class.java) as Map<String, Any>

            EventTrackHelper.doEventTrack(type, mapParams)
        }

        @JavascriptInterface
        @Suppress("MaxLineLength")
        fun isDarkMode(jsonString: String) {
            val isDarkMode = when (AppCompatDelegate.getDefaultNightMode()) {
                AppCompatDelegate.MODE_NIGHT_YES -> true
                AppCompatDelegate.MODE_NIGHT_NO -> false
                AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM, AppCompatDelegate.MODE_NIGHT_AUTO_BATTERY -> {
                    // 检查系统设置
                    val currentNightMode = context.resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
                    currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES
                }
                else -> false
            }
            executeJsCallback(webView, jsonString, isDarkMode.toString())
        }

        // 跳转到停车位详情界面
        @JavascriptInterface
        fun navigateToPage(jsonString: String) {
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess")
            val data = jsonObj.getJSONObject("data")
            val url = data.getString("url")
            val pageType = data.getString("pageType")
            listener.nativeToPage(url, pageType)

            // 强制在主线程执行 WebView 操作
            webView.post {
                // 处理业务逻辑（例如更新数据）
                // 调用 JavaScript 回调函数
                val jsCallback = "$onSuccess('true')"

                Timber.d(TAG, "call navigateToPage method: $jsCallback  ")
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.d(TAG, "JS callback result: $result")
                }
            }
        }

        @JavascriptInterface
        fun toast(jsonString: String) {
            Timber.tag(TAG).i("toast: $jsonString")
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val onFail = jsonObj.getString("onFail") // / 获取固定失败callback Name
            val data = jsonObj.getJSONObject("data") // 获取其他参数
            Timber.tag(TAG).d("data = $data")
            val txt = data.getString("txt")
            val during = data.getInt("during")

            // 强制在主线程执行 WebView 操作
            webView.post {
                val now = System.currentTimeMillis()
                val sameText = txt == lastToastText
                val withinDebounce = now - lastToastTime < TOAST_DEBOUNCE_MS
                // 只有“相同文案 + 2 秒内”才跳过弹窗，但回调仍然执行
                if (!(sameText && withinDebounce)) {
                    currentToast?.cancel()
                    val toast = Toast.makeText(
                        context.applicationContext,
                        txt,
                        if (during <= 1) Toast.LENGTH_SHORT else Toast.LENGTH_LONG
                    )
                    toast.show()
                    currentToast = toast
                    lastToastText = txt
                    lastToastTime = now
                }
                // 每次都会回调，前端不会重刷
                webView.evaluateJavascript("$onSuccess('true')") {}
            }
        }

        @JavascriptInterface
        fun onContentLoadFinish() {
            listener.onContentLoadFinish()
        }
    }
}

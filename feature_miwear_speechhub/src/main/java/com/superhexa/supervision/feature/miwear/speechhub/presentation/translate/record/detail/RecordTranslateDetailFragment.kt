package com.superhexa.supervision.feature.miwear.speechhub.presentation.translate.record.detail

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.compont.RecordTime
import com.superhexa.supervision.feature.miwear.speechhub.compont.RecordTranslateError
import com.superhexa.supervision.feature.miwear.speechhub.compont.RecordTranslateOrigin
import com.superhexa.supervision.feature.miwear.speechhub.compont.RecordTranslateTarget
import com.superhexa.supervision.feature.miwear.speechhub.compont.RecordTranslateTitleBar
import com.superhexa.supervision.feature.miwear.speechhub.data.KeyWord
import com.superhexa.supervision.feature.miwear.speechhub.data.LanguageItem
import com.superhexa.supervision.library.base.basecommon.extension.clearKeepScreenOn
import com.superhexa.supervision.library.base.basecommon.extension.keepScreenOn
import com.superhexa.supervision.library.base.basecommon.extension.safeActivity
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.tools.JsonUtils
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.ai.capability.constant.ScenarioType
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.aivs.track.EventTrack
import org.kodein.di.generic.instance
import com.superhexa.supervision.library.string.R as StringR

class RecordTranslateDetailFragment : BaseComposeFragment() {
    private val viewModel by instance<RecordTranslateDetailViewModel>()
    private var enterTime: Long = 0L

    @SuppressLint("NewApi")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AiCapabilityWrapper.abnormalStopsReasonList.clear()
        enterTime = System.currentTimeMillis()
        // 同声传译时开启屏幕常亮
        keepScreenOn()
        arguments?.let {
            val origin = it.getParcelable<LanguageItem>(KeyWord.ORIGIN_LANG)
            val target = it.getParcelable<LanguageItem>(KeyWord.TARGET_LANG)
            viewModel.sendEvent(
                RecordTDEvent.Init(requireActivity(), origin!!, target!!, viewLifecycleOwner)
            )
        }
        switchTitleBarColor(false)
        O95Statistic.exposeRecordTranslateLive()
    }

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color18191A)
        ) {
            val (title, time, content, error) = createRefs()
            RecordTranslateTitleBar(
                modifier = Modifier
                    .constrainAs(title) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                    }
                    .fillMaxWidth(),
                state = state
            ) {
                navigator.pop()
                O95Statistic.clickRecordTranslateLive("exit_button")
            }
            RecordTime(
                modifier = Modifier
                    .constrainAs(time) {
                        top.linkTo(title.bottom)
                        start.linkTo(parent.start)
                    }
                    .fillMaxWidth(),
                enable = state.value.translateOk,
                seconds = state.value.duration
            ) {
                // viewModel.sendEvent(RecordTDEvent.OnTimeChange(it))
            }
            Column(
                modifier = Modifier
                    .constrainAs(content) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(time.bottom)
                        bottom.linkTo(parent.bottom)
                        height = Dimension.fillToConstraints
                    }
                    .fillMaxWidth()
            ) {
                RecordTranslateOrigin(
                    modifier = Modifier.weight(1f),
                    content = state.value.originTxt
                )
                RecordTranslateTarget(
                    modifier = Modifier
                        .weight(1f)
                        .background(ColorBlack),
                    ttsEnable = state.value.ttsEnable,
                    content = state.value.targetTxt
                ) {
                    viewModel.sendEvent(RecordTDEvent.TtsEnableToggle)
                    O95Statistic.clickRecordTranslateLive("pause_button")
                }
            }
            if (!state.value.translateOk) {
                RecordTranslateError(
                    modifier = Modifier
                        .constrainAs(error) {
                            start.linkTo(parent.start)
                            bottom.linkTo(parent.bottom)
                        }
                        .fillMaxWidth(),
                    errorState = viewModel.errorState
                ) {
                    viewModel.sendEvent(RecordTDEvent.RestartTranslate)
                }
            }
        }
        LaunchedEffect(viewModel) {
            viewModel.mEffect.collect { effect -> handelEffect(effect) }
        }
    }

    private fun handelEffect(effect: RecordTDEffect) {
        when (effect) {
            is RecordTDEffect.ShowTips -> requireContext().toast(effect.msg)
            is RecordTDEffect.Toast -> requireContext().toast(effect.textId)
            RecordTDEffect.ExitByTimeLimit -> {
                // 显示时长限制提示并退出页面
                requireContext().toast(StringR.string.record_translate_time_limit_toast)
                navigator.pop()
            }
        }
    }

    private fun switchTitleBarColor(isDefault: Boolean) {
        safeActivity()?.let {
            StatusBarUtil.setStatusBarColor(
                it,
                colorRes = if (isDefault) R.color.pageBackground else R.color.color_18191A
            )
        }
    }

    override fun onDestroyView() {
        switchTitleBarColor(true)
        // 离开页面时关闭屏幕常亮
        clearKeepScreenOn()
        val abnormalStopsReason = JsonUtils.toJson(AiCapabilityWrapper.abnormalStopsReasonList)
        val exitTime = System.currentTimeMillis()
        AiCapabilityWrapper.abnormalStopsReasonList.lastOrNull()?.code?.let {
            EventTrack.trackRecordTranslateEvent(
                scenarioType = ScenarioType.GLASSES_TRANSLATION,
                activityType = "simultaneous interpreting",
                abnormalStopsNum = AiCapabilityWrapper.abnormalStopsReasonList.size,
                abnormalStopsReason = abnormalStopsReason,
                duration = exitTime - enterTime,
                endType = it
            )
        }
        super.onDestroyView()
    }
}

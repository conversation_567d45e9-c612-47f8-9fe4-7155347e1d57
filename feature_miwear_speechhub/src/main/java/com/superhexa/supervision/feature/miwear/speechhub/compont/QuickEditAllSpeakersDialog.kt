@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.miwear.speechhub.compont

import android.annotation.SuppressLint
import androidx.annotation.Keep
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription.AudioTranscriptionViewModel
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.EditText
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.extension.clickDebounceNoEffect
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_132
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
import com.superhexa.supervision.library.base.basecommon.theme.Dp_25
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.xiaomi.ai.capability.request.model.Phrase
import com.xiaomi.aivs.utils.NetWorkUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val SHOW_PLAY_ITEM_MAX = 2

@SuppressLint("StateFlowValueCalledInComposition")
@Suppress("LongParameterList", "LongMethod", "ComplexMethod")
@Composable
fun QuickEditAllSpeakersDialog(
    viewModel: AudioTranscriptionViewModel,
    visible: Boolean,
    maxLength: Int,
    phraseList: List<AudioTranscriptionViewModel.SpeakPhrase>,
    buttonConfig: ButtonConfig.TwoButton,
    onDismiss: () -> Unit,
    onPlayStart: (offsetMs: Long, duration: Long) -> Unit,
    onPlayPause: () -> Unit,
    onConfirm: (List<SpeakerChangeInfo>) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    val playItems = remember { mutableStateListOf<SpeakContentPlayItem>() }
    val playStatusMap = remember { mutableStateMapOf<Long, Boolean>() }
    var isDuringCheckingInputText by remember { mutableStateOf(false) }

    fun updatePlayItem(playItem: SpeakContentPlayItem) {
        // 经过产品确认，这里不需要做合并同类项，用户下次进入的时候合并即可
        val index = playItems.indexOfFirst { it.speakerId == playItem.speakerId }
        if (index >= 0) {
            playItems[index] = playItem
        }
    }

    fun updatePlayStatus(currentPlayingId: Long?) {
        playStatusMap.keys.forEach { id ->
            playStatusMap[id] = (id == currentPlayingId)
        }
    }

    fun updateIsFocusSpeaker(playItem: SpeakContentPlayItem) {
        // 这里涉及到被修改成同名的情况，用speakerName匹配
        playItems.replaceAll { item ->
            if (item.speakerName == playItem.speakerName) {
                // 切换 isFocusSpeaker 并更新 phrases
                item.copy(
                    isFocusSpeaker = !item.isFocusSpeaker
                )
            } else {
                item // 不同名字的保持原样
            }
        }
    }

    LaunchedEffect(visible) {
        if (visible) {
            playItems.clear()
            playItems.addAll(generatePlayItems(phraseList))
            playStatusMap.clear()
            phraseList.forEach { phrase ->
                playStatusMap[phrase.objId] = false
            }
        }
    }

    BottomSheet(visible = visible, onDismiss = onDismiss) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 733.dp)
        ) {
            Spacer(modifier = Modifier.height(Dp_28))
            Text(
                text = stringResource(R.string.text_edit_speaker_name),
                style = TextStyle(
                    color = ColorWhite,
                    fontSize = Sp_16,
                    fontWeight = FontWeight.W500
                ),
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Dp_28)
            )
            Spacer(modifier = Modifier.height(Dp_20))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .verticalScroll(scrollState)
            ) {
                playItems.forEach { playItem ->
                    key(playItem.speakerId) {
                        EditText(
                            modifier = Modifier.onFocusChanged { focusState ->
                                // 监听焦点变化来决定是否显示历史记录
                                val newIsEditing = focusState.isFocused
                                if (playItem.isEditing != newIsEditing) {
                                    updatePlayItem(playItem.copy(isEditing = newIsEditing))
                                }
                            },
                            input = playItem.speakerName,
                            placeholder = playItem.speakerName,
                            maxLength = maxLength,
                            needTopPadding = false,
                            onValueChange = { newName ->
                                if (newName != playItem.speakerName) {
                                    updatePlayItem(
                                        playItem.copy(
                                            speakerName = newName,
                                            isNameChanged = true
                                        )
                                    )
                                }
                            }
                        )
                        if (playItem.isEditing) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = Dp_28)
                                    .background(
                                        color = colorResource(R.color.color_222425),
                                        shape = RoundedCornerShape(Dp_16)
                                    )
                                    .padding(vertical = Dp_16)
                            ) {
                                RecentSearchScreen(
                                    allHistory = viewModel.speakerNameHistory.value,
                                    onSearchHistoryClick = {
                                        updatePlayItem(playItem.copy(speakerName = it))
                                    },
                                    onItemDelete = { item ->
                                        viewModel.deleteSingleSpeakerNameHistory(item)
                                    },
                                    onAllItemDelete = {
                                        viewModel.deleteAllSpeakerNameHistory()
                                    }
                                )
                            }
                        }

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = Dp_28, vertical = 13.5.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "总计 ${generateTimeDurationFormat(
                                    playItem.phrases.map { it.phrase.durationMilliseconds.toLong() }
                                )}",
                                style = TextStyle(
                                    fontSize = Sp_16,
                                    fontWeight = FontWeight.W400,
                                    color = colorResource(R.color.white)
                                ),
                                modifier = Modifier.weight(1f),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            Row(
                                modifier = Modifier
                                    .width(Dp_132)
                                    .clickDebounceNoEffect {
                                        updateIsFocusSpeaker(playItem)
                                    },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.text_mark_as_focus_speaker_title),
                                    style = TextStyle(
                                        fontSize = Sp_13,
                                        fontWeight = FontWeight.W400,
                                        color = colorResource(R.color.white_60)
                                    ),
                                    modifier = Modifier.weight(1f),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                                Spacer(modifier = Modifier.width(Dp_4))
                                val imageId = if (playItem.isFocusSpeaker) {
                                    R.drawable.ic_radio_selected
                                } else {
                                    R.drawable.ic_radio_default
                                }
                                Image(
                                    painter = painterResource(id = imageId),
                                    contentDescription = stringResource(R.string.text_mark_as_focus_speaker_title),
                                    modifier = Modifier.size(Dp_16)
                                )
                            }
                        }

                        playItem.phrases.take(SHOW_PLAY_ITEM_MAX).forEach { speakPhrase ->
                            PlayItem(
                                objId = speakPhrase.objId,
                                phrase = speakPhrase.phrase,
                                isPlaying = playStatusMap[speakPhrase.objId] ?: false,
                                onPlayStart = { objId, offsetMs, duration ->
                                    updatePlayStatus(objId)
                                    onPlayStart(offsetMs, duration)
                                },
                                onPlayPause = {
                                    updatePlayStatus(null)
                                    onPlayPause.invoke()
                                }
                            )
                        }

                        Spacer(modifier = Modifier.height(Dp_4))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(Dp_25)
                                .padding(end = Dp_28),
                            horizontalArrangement = Arrangement.End,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "共${playItem.phrases.size}段",
                                style = TextStyle(
                                    fontSize = Sp_13,
                                    fontWeight = FontWeight.W400,
                                    color = colorResource(R.color.white_60)
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                        Spacer(modifier = Modifier.height(Dp_8))
                    }
                }
            }

            Row(modifier = Modifier.padding(top = Dp_40)) {
                SubmitButton(
                    subTitle = buttonConfig.button2.text,
                    enable = true,
                    enableColors = buttonConfig.button2.enableColors,
                    disableColors = buttonConfig.button2.disableColors,
                    textColor = Color.White,
                    modifier = Modifier
                        .padding(start = Dp_28, end = Dp_5, bottom = Dp_28)
                        .weight(1f)
                ) {
                    onDismiss.invoke()
                }
                SubmitButton(
                    subTitle = buttonConfig.button1.text,
                    enable = !isDuringCheckingInputText,
                    enableColors = if (playItems.any { it.speakerName.trim().isEmpty() }) {
                        listOf(Color26EAD9_30, Color17CBFF_30)
                    } else {
                        listOf(Color26EAD9, Color17CBFF)
                    },
                    textColor = ColorBlack,
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
                    modifier = Modifier
                        .padding(start = Dp_5, end = Dp_28, bottom = Dp_28)
                        .weight(1f)
                ) {
                    if (playItems.any { it.speakerName.trim().isEmpty() }) {
                        context.toast(R.string.text_rename_is_empty)
                        return@SubmitButton
                    }

                    if (
                        playItems.any {
                            it.speakerName.isNotBlank() && it.speakerName.length > maxLength
                        }
                    ) {
                        context.toast(R.string.text_rename_speaker_over_limit)
                        return@SubmitButton
                    }

                    scope.launch(Dispatchers.Main) {
                        val pendingAddedItems = mutableListOf<String>()
                        var isSuccess = true
                        isDuringCheckingInputText = true
                        playItems.forEach { playItem ->
                            val modifiedName = playItem.speakerName
                            val originalName = playItem.phrases.first().speakName
                            if (
                                modifiedName != originalName &&
                                !viewModel.speakerNameHistory.value.contains(modifiedName)
                            ) {
                                if (!NetWorkUtil.isNetWorkValidated(context)) {
                                    // 没网络，弹提示，不执行后续逻辑
                                    context.toast(R.string.text_no_network_for_check)
                                    isSuccess = false
                                    isDuringCheckingInputText = false
                                    return@launch
                                } else {
                                    // 有网络，要做黄反检测
                                    val result = withContext(Dispatchers.IO) {
                                        viewModel.checkSensitiveContent(modifiedName)
                                    }

                                    if (!result.second) {
                                        context.toast(
                                            "[${result.first}]" + context.getString(
                                                R.string.text_check_sensitive_content_failed
                                            )
                                        )
                                        isSuccess = false
                                    }
                                }
                            }
                            if (needAddToHistory(playItem, viewModel.speakerNameHistory.value)) {
                                pendingAddedItems.add(playItem.speakerName)
                            }
                        }
                        isDuringCheckingInputText = false
                        if (isSuccess) {
                            viewModel.addSpeakerNameHistory(pendingAddedItems)
                            onConfirm(generateConfirmResult(playItems))
                        }
                    }
                }
            }
        }
    }
}

@Suppress("LongMethod")
@Composable
private fun PlayItem(
    objId: Long,
    phrase: Phrase,
    isPlaying: Boolean,
    onPlayStart: (objId: Long, offsetMs: Long, duration: Long) -> Unit,
    onPlayPause: () -> Unit
) {
    val scope = rememberCoroutineScope()
    var startAutoStopJob by remember { mutableStateOf<Job?>(null) }

    // 仅用于更新UI
    fun startAutoStopJob(duration: Long) {
        startAutoStopJob?.cancel()
        startAutoStopJob = scope.launch {
            delay(duration)
            onPlayPause()
            startAutoStopJob = null
        }
    }

    LaunchedEffect(isPlaying) {
        if (isPlaying) {
            startAutoStopJob(phrase.durationMilliseconds.toLong())
        } else {
            startAutoStopJob?.cancel()
            startAutoStopJob = null
        }
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_28, vertical = Dp_12),
        verticalAlignment = Alignment.Top
    ) {
        // 左侧播放按钮
        PlayPauseButton(
            painter = painterResource(
                id = if (isPlaying) R.drawable.icon_pause else R.drawable.icon_play
            ),
            modifier = Modifier.size(Dp_24),
            onClick = {
                if (!isPlaying) {
                    onPlayStart(
                        objId,
                        phrase.offsetMilliseconds.toLong(),
                        phrase.durationMilliseconds.toLong()
                    )
                    startAutoStopJob(phrase.durationMilliseconds.toLong())
                } else {
                    startAutoStopJob?.cancel()
                    onPlayPause.invoke()
                }
            }
        )

        Spacer(modifier = Modifier.width(12.dp))

        // 右侧内容
        Column(modifier = Modifier.weight(1f)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = formatTimeRange(
                        offsetMs = phrase.offsetMilliseconds.toLong(),
                        duration = phrase.durationMilliseconds.toLong()
                    ),
                    style = TextStyle(
                        fontSize = Sp_16,
                        fontWeight = FontWeight.W500,
                        color = colorResource(if (isPlaying) R.color.color_55D8E4 else R.color.white)
                    ),
                    modifier = Modifier.widthIn(max = 157.dp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.width(Dp_12))

                Text(
                    text = generateTimeDurationFormat(
                        listOf(phrase.durationMilliseconds.toLong())
                    ),
                    style = TextStyle(
                        fontSize = Sp_16,
                        fontWeight = FontWeight.W400,
                        color = colorResource(if (isPlaying) R.color.color_55D8E4 else R.color.white)
                    ),
                    modifier = Modifier.widthIn(max = 157.dp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(Dp_8))

            Text(
                text = phrase.text,
                style = TextStyle(
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    color = colorResource(if (isPlaying) R.color.color_55D8E4 else R.color.white_60)
                ),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

private fun generatePlayItems(
    phraseList: List<AudioTranscriptionViewModel.SpeakPhrase>
): List<SpeakContentPlayItem> {
    return phraseList
        .groupBy { it.speakName }
        .entries
        .mapIndexed { index, entry ->
            SpeakContentPlayItem(
                speakerId = index,
                speakerName = entry.key,
                isFocusSpeaker = entry.value.first().isFocusSpeaker,
                phrases = entry.value
            )
        }
}

private fun generateTimeDurationFormat(durations: List<Long>): String {
    val totalDuration = durations.sum()
    return DateTimeUtils.formatDuration(totalDuration)
}

private fun formatTimeRange(offsetMs: Long, duration: Long): String {
    val startTimeFormat = DateTimeUtils.videoDuration(offsetMs)
    val endTimeFormat = DateTimeUtils.videoDuration(offsetMs + duration)
    return "$startTimeFormat - $endTimeFormat"
}

private fun needAddToHistory(
    playItem: SpeakContentPlayItem,
    allHistory: List<String>
): Boolean {
    return playItem.speakerName.isNotBlank() &&
        playItem.isNameChanged &&
        !allHistory.contains(playItem.speakerName)
}

private fun generateConfirmResult(
    playItems: List<SpeakContentPlayItem>
): List<SpeakerChangeInfo> {
    return playItems.mapNotNull { item ->
        val newName = item.speakerName
        val oldName = item.phrases.first().speakName
        val newFocus = item.isFocusSpeaker
        val oldFocus = item.phrases.first().isFocusSpeaker

        if (newName != oldName || newFocus != oldFocus) {
            SpeakerChangeInfo(
                newName = newName,
                oldName = oldName,
                isFocus = newFocus,
                affectedObjIds = item.phrases.map { it.objId }
            )
        } else {
            null
        }
    }
}

@Keep
data class SpeakContentPlayItem(
    val speakerId: Int,
    val speakerName: String,
    val isEditing: Boolean = false,
    val isNameChanged: Boolean = false,
    val isFocusSpeaker: Boolean,
    val phrases: List<AudioTranscriptionViewModel.SpeakPhrase>
)

@Keep
data class SpeakerChangeInfo(
    val newName: String,
    val oldName: String,
    val isFocus: Boolean,
    val affectedObjIds: List<Long>
)

package com.superhexa.supervision.feature.profile.presentation.setting

import android.Manifest
import android.content.ActivityNotFoundException
import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.os.Bundle
import android.os.SystemClock
import android.provider.Settings
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager.globalModel
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.profile.R
import com.superhexa.supervision.feature.profile.databinding.FragmentSettingBinding
import com.superhexa.supervision.feature.profile.presentation.router.HexaRouter
import com.superhexa.supervision.feature.profile.presentation.router.ProfileModuleImpl
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FirmWarreAutoDownload
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.showToast
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.permission.PermissionsUtils
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.login.ILoginModuleApi
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.net.retrofit.HostDataManager
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_MINE
import com.xiaomi.aivs.engine.helper.LocationHelper
import kotlinx.coroutines.ExperimentalCoroutinesApi
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:我的设置页面
 * 创建日期: 2021/8/11
 * 作者: QinTaiyuan
 */

@Suppress("MagicNumber")
@ExperimentalCoroutinesApi
@Route(path = RouterKey.profile_SettingFragment)
class SettingFragment : InjectionFragment(R.layout.fragment_setting) {
    private val count = 10 // 点击次数
    private val duration: Long = 2000 // 规定有效时间
    private var mHits = LongArray(count)
    private val viewBinding: FragmentSettingBinding by viewBinding()
    private val viewModel: SettingViewModel by instance()
    private val accountManager: AccountManager by instance()
    private val appEnvironment by instance<AppEnvironment>()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
//        viewBinding.test.visibleOrgone(MMKVUtils.decodeBoolean(DevelopModelOpen, false))
        initData()

        O95Statistic.exposeTip43030("My_Page")
    }

    @Suppress("LongMethod")
    private fun initListener() {
        viewBinding.tvUserAccount.setOnClickListener {
            continuousClick(count, duration)
        }
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
//        viewBinding.stNickName.clickDebounce(viewLifecycleOwner) {
//            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_PROFILE)
//            HexaRouter.Profile.navigateToPersion(this@SettingFragment)
//        }
        viewBinding.stAccount.clickDebounce(viewLifecycleOwner) {
            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_ACCOUNT)
            O95Statistic.clickMyPageEvent("account_item")
            HexaRouter.Profile.navigateToAccountManager(this@SettingFragment)
        }
//        viewBinding.stNation.clickDebounce(viewLifecycleOwner) {
//            HexaRouter.Profile.navigateToNation(this@SettingFragment)
//        }
        viewBinding.stHistory.clickDebounce(viewLifecycleOwner) {
            O95Statistic.clickMyPageEvent("history_file_item")
            HexaRouter.Profile.navigateToHistoryFile(this@SettingFragment)
        }
        viewBinding.stStore.clickDebounce(viewLifecycleOwner) {
            if (ActivityCompat.checkSelfPermission(
                    LibBaseApplication.instance,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                PermissionsUtils.requestPermissionWithHiddenFragment(
                    this@SettingFragment,
                    mutableListOf(Manifest.permission.ACCESS_FINE_LOCATION)
                ) {
                    enterGlassesStorePage()
                }
            } else {
                enterGlassesStorePage()
                O95Statistic.clickMyPageEvent("store_info")
            }
        }

        viewBinding.stAbout.clickDebounce(viewLifecycleOwner) {
            O95Statistic.clickMyPageEvent("about_app")
            HexaRouter.Profile.navigateToAboutApp(this@SettingFragment)
        }

        viewBinding.tvSignOut.clickDebounce(viewLifecycleOwner) {
            val isMonkey = com.superhexa.supervision.feature.profile.BuildConfig.MONKEY_PACKAGE
            if (!isMonkey) {
                signOut()
            }
        }
        viewBinding.firmWarreSwitch.isChecked = MMKVUtils.decodeBoolean(FirmWarreAutoDownload, true)
        viewBinding.firmWarreSwitch.setOnCheckedChangeListener { _, isChecked ->
            StatisticHelper.addEventProperty("act_type", isChecked)
                .doEvent(EventCons.EventKey_SV1_AUTO_DOWNLOAD_FIRMWARE)
            MMKVUtils.encode(FirmWarreAutoDownload, isChecked)
        }

        viewBinding.stQA.clickDebounce(viewLifecycleOwner) {
            if (com.superhexa.supervision.feature.profile.BuildConfig.FLAVOR.contains(ConstsConfig.FlavorGlobal)) {
                var decodeString = MMKVUtils.decodeString(HostDataManager.BASE_URL_FIELD_NAME)
                if (decodeString.isNullOrBlank()) {
                    decodeString = ConstantUrls.BASE_URL
                }
                val onLine = decodeString == ConstantUrls.BASE_URL
                HexaRouter.Web.navigateToWebView(
                    fragment = this@SettingFragment,
                    url = if (onLine) ConstantUrls.Q_A_URL else ConstantUrls.Q_A_URL_TEST,
                    extentText = getString(R.string.settingHelp)
                ) {
                    HexaRouter.Profile.navigateToQuestionFeedback(this@SettingFragment, globalModel)
                }
            } else {
                O95Statistic.clickMyPageEvent("help_center")
                HexaRouter.Profile.navigateToHelpFragment(this@SettingFragment)
            }
        }
        viewBinding.test.clickDebounce(viewLifecycleOwner) {
            viewModel.checkBTSpeed {
            }
        }
    }

    private fun enterGlassesStorePage() {
        LocationHelper().getLocation(LibBaseApplication.instance) { location: Location? ->
            Timber.d("location ${location?.latitude}, ${location?.longitude}")
            val storeUrl = location?.let {
                val storeUrl = StringBuilder(ConstantUrls.GLASSES_STORE_URL)
                    .append("&latitude=%s&longitude=%s")
                    .toString()
                val url = String.format(storeUrl, it.latitude, it.longitude)
                Timber.d("toStore url :$url")
                url
            } ?: run {
                ConstantUrls.GLASSES_STORE_URL
            }

            try {
                val intent = Intent(Intent.ACTION_VIEW, storeUrl.toUri())
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                LibBaseApplication.instance.startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                Timber.e("enterStorePage occur e:${e.message}")
                LibBaseApplication.instance.toast("未找到可打开此链接的应用")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        val accountInfo = accountManager.accountLiveData.value
        when (BuildConfig.DEVELOPER_BUILD) {
            "XIAOMI" -> {
                viewBinding.tvUserAccount.text =
                    requireContext().getString(R.string.settingAccount)
                        .format(accountInfo?.userId.toString())
            }

            else -> {
                viewBinding.tvUserAccount.visibleOrgone(accountInfo?.phone.isNotNullOrEmpty())
                viewBinding.tvUserAccount.text = requireContext().getString(R.string.settingAccount)
                    .format(hidePhoneNo(accountInfo?.phone ?: ""))
            }
        }
        val countryRegion = MMKVUtils.decodeString(ConstsConfig.CountryRegionCountry)
        viewBinding.stNation.setDesc(countryRegion)
    }

    private fun initData() {
        viewModel.signOutCallback.observe(viewLifecycleOwner) {
            when (it) {
                SettingViewModel.SignOutState.Start -> {
                    showLoading()
                }

                SettingViewModel.SignOutState.Success -> {
                    O95Statistic.logout()
                    hideLoading()
                    signOutSuccess()
                }

                SettingViewModel.SignOutState.Failed -> {
                    hideLoading()
                    signOutSuccess()
                }
            }
        }

        UpgradeManager.updateLiveData.observe(viewLifecycleOwner) {
            viewBinding.stAbout.setDotVisible(it)
        }
        viewBinding.clFirmContent.visibleOrgone(!isSSModel())
    }

    private fun signOutSuccess() {
        ProfileModuleImpl().clearDataWhenSignOut()
        when (BuildConfig.DEVELOPER_BUILD) {
            "XIAOMI" -> HexaRouter.Login.navigateToPassportLogin(this)
            else -> HexaRouter.Login.navigateToLogin(this)
        }
    }

    private fun signOut() {
        Timber.d("signOut:${ILoginModuleApi::class.java.impl.isUseSystem()}")
        if (!BuildConfig.DEBUG && appEnvironment.isMIUI() && ILoginModuleApi::class.java.impl.isUseSystem()) {
            val intent = Intent()
            intent.setComponent(ComponentName("com.xiaomi.account", "com.xiaomi.account.ui.AccountSettingsActivity"))
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            try {
                O95Statistic.logout()
                startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                // 处理未找到活动的异常
                val settingIntent = Intent(Settings.ACTION_SETTINGS)
                settingIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                try {
                    O95Statistic.logout()
                    ContextCompat.startActivity(requireActivity(), settingIntent, null)
                } catch (e: ActivityNotFoundException) {
                    // 处理异常情况
                    viewModel.signOut()
                }
            }
        } else {
            viewModel.signOut()
        }
    }

    private fun isSSModel(): Boolean {
        val lastDevice = BlueDeviceDbHelper.getBondDevice()
        return when {
            com.superhexa.supervision.feature.profile.BuildConfig.FLAVOR == ConstsConfig.FlavorGlobal -> false
            lastDevice?.model == mainlandModel || lastDevice?.model == globalModel -> false
            else -> true
        }
    }

    private fun hidePhoneNo(phoneNo: String): String {
        val length = phoneNo.length - 1
        val beforeLength = index
        val afterLength = index
        // 替换字符串，当前使用“*”
        val replaceSymbol = "*"
        val sb = StringBuffer()
        for (i in 0..length) {
            if (i < beforeLength || i >= (length - afterLength)) {
                sb.append(phoneNo[i])
            } else {
                sb.append(replaceSymbol)
            }
        }
        return sb.toString()
    }

    companion object {
        private const val index = 3
    }

    override fun getPageName() = ScreenName_SV1_MINE

    // 多次点击开启调试环境
    private fun continuousClick(count: Int, time: Long) {
        System.arraycopy(mHits, 1, mHits, 0, mHits.size - 1)
        mHits[mHits.size - 1] = SystemClock.uptimeMillis()
        if (mHits[0] >= SystemClock.uptimeMillis() - duration) {
            var developOpen = MMKVUtils.decodeBoolean(ConstsConfig.DevelopModelOpen)
            MMKVUtils.encode(ConstsConfig.DevelopModelOpen, !developOpen)
            showToast(LibBaseApplication.instance, "开发者模式已${if (!developOpen) "开启" else "关闭"}", 0)
            mHits = LongArray(count)
        }
    }
}

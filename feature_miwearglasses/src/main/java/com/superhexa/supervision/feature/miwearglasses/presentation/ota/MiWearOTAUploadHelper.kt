package com.superhexa.supervision.feature.miwearglasses.presentation.ota

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import okhttp3.OkHttpClient
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @date 2025/6/27 11:00.
 * description：OTA升级相关能力主要是进行上传
 */
class MiWearOTAUploadHelper(context: Context, params: WorkerParameters) : CoroutineWorker(context, params) {

    companion object {
        const val KEY_FILE_PATH = "file_path"
        const val KEY_URL = "upload_url"
        const val KEY_MD5 = "md5"
        private const val TIME_OUT = 30L
        private const val WRITE_TIME_OUT = 30L
    }

    private val okHttpClient by lazy {
        OkHttpClient.Builder()
            .connectTimeout(TIME_OUT, TimeUnit.SECONDS)
            .readTimeout(TIME_OUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIME_OUT, TimeUnit.SECONDS)
            .retryOnConnectionFailure(false)
            .build()
    }

    @Suppress("ReturnCount")
    override suspend fun doWork(): Result {
        val filePath = inputData.getString(KEY_FILE_PATH) ?: return Result.failure()
        val url = inputData.getString(KEY_URL) ?: return Result.failure()
        val md5 = inputData.getString(KEY_MD5) ?: ""

        val file = File(filePath)
        if (!file.exists()) return Result.failure()

        val result = MiWearOTAApiService.uploadFile(okHttpClient, url, file, md5) { progress ->
            setProgressAsync(workDataOf("progress" to progress))
        }

        Timber.d("do work result ==> $result")
        return if (result.isSuccess) Result.success() else Result.failure()
    }
}

package com.superhexa.supervision.feature.miwearglasses.presentation.setting

import BrightDialog
import SettingItemDesc
import VideoDurationDialog
import VolumeDialog
import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.music.MusicApiService
import com.superhexa.music.data.MusicCp
import com.superhexa.music.data.RadioStationCp
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorLight.Companion.BRIGHT_CLOSE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorLight.Companion.BRIGHT_HIGH
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorLight.Companion.BRIGHT_LOW
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorLight.Companion.BRIGHT_MIDDLE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorVolume.Companion.VOLUME_AUTO
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorVolume.Companion.VOLUME_HIGH
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorVolume.Companion.VOLUME_LOW
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetIndicatorVolume.Companion.VOLUME_MIDDLE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.DirectionMode.DIRECTION_ALL
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.DirectionMode.DIRECTION_FOR_WORD
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.MuSicMode.MUSIC_DOUBLE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.MuSicMode.MUSIC_SINGLE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.ShieldMode.SHIELD_CLOSE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.ShieldMode.SHIELD_OPEN
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.TempControlMode.TEMP_NONE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.VideoDuration.SECONDS_120
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.VideoDuration.SECONDS_15
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.VideoDuration.SECONDS_180
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.VideoDuration.SECONDS_300
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.VideoDuration.SECONDS_60
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SetMediaSetting.Companion.VideoDuration.SECONDS_600
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.CameraJointDetectionManager
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.DeviceCompanionManager
import com.superhexa.supervision.feature.channel.presentation.newversion.nearby.MMANearbyManager
import com.superhexa.supervision.feature.miwearglasses.BuildConfig
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.presentation.collaboration.MiWearCameraUpgradeChecker
import com.superhexa.supervision.feature.miwearglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.FullDuplexTimeoutDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.MusicControlDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.MusicSwitchDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.NewBindDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.OtherDeviceDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.RadioDirectionDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.RadioStationSwitchDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.ShootModeDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.TTSSwitchDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.component.TempControlDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.unbind.MiWearUnBindDialogFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.unbind.MiWearUnBindStateDialogFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.unbind.MiWearUnBindStateDialogFragment.Companion.UNBINDING_STATE
import com.superhexa.supervision.feature.miwearglasses.presentation.unbind.MiWearUnBindStateDialogFragment.Companion.UNBIND_FAILED_STATE
import com.superhexa.supervision.feature.miwearglasses.presentation.unbind.MiWearUnBindStateDialogFragment.Companion.UNBIND_SUCCESS_STATE
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.MiWearUpgradeInfo
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowRedDot
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitch
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDes
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.MIWEAR_SETTING_MUSIC_SOURCE
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.DevelopModelOpen
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ENABLE_DUMP
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ENABLE_WATERMARK
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.isVersionGreaterOrEqual
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_41
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.xiaomi.aivs.engine.helper.MusicSource
import com.xiaomi.aivs.engine.helper.MusicSource.Companion.DEFAULT
import com.xiaomi.aivs.engine.helper.MusicSource.Companion.NET_EASE
import com.xiaomi.aivs.engine.helper.MusicSource.Companion.QQ
import com.xiaomi.aivs.engine.helper.RadioStationSource
import com.xiaomi.aivs.engine.helper.RadioStationSource.Companion.FM
import com.xiaomi.aivs.utils.PackageHelper
import com.xiaomi.wear.protobuf.nano.SystemProtos
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/9/5 on 19:30
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions", "LargeClass")
@Route(path = RouterKey.miwearglasses_MiWearSettingFragment)
class MiWearSettingFragment : BaseComposeFragment() {
    private val viewModel by instance<MiWearSettingViewModel>()
    private var did: String? = null
    private var needShowMusciSource: Boolean = false
    private var unBindingDialog: MiWearUnBindStateDialogFragment? = null
    private var enterTime = 0L
    private val pair = O95Statistic.eventGlassSettingFinishPair()
    private val appEnvironment by instance<AppEnvironment>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        did = arguments?.getString(BundleKey.MIWEAR_SETTING_SID)
        Timber.i("onViewCreated called did:$did")
        needShowMusciSource = arguments?.getBoolean(MIWEAR_SETTING_MUSIC_SOURCE) ?: false
        showLoading()
        viewModel.sendEvent(
            MiWearSettingEvent.InitBondDevice(did) {
                hideLoading()
            }
        )
        if (!viewModel.deviceStateLiveData.isSuccessState()) {
            viewModel.deviceStateLiveData.observeState(viewLifecycleOwner, O95State::deviceState) {
                when (it) {
                    is DeviceState.ChannelSuccess -> {
                        refreshItems()
                    }

                    else -> {}
                }
            }
        }
        enterTime = System.currentTimeMillis()
    }

    private fun refreshItems() = launch {
        Timber.d("onChannelSuccess---refreshItems")
        showLoading()
        viewModel.sendEvent(
            MiWearSettingEvent.InitBondDevice(did) {
                hideLoading()
            }
        )
    }

    @Suppress("MagicNumber")
    override fun onResume() {
        super.onResume()
        viewModel.sendEvent(MiWearSettingEvent.GetWifiConfig)
        viewModel.sendEvent(MiWearSettingEvent.SyncBackLocationCommand)
        viewModel.sendEvent(MiWearSettingEvent.SyncMusicSourceSettingCommand)
        if (needShowMusciSource) {
            lifecycleScope.launch {
                delay(1200)
                viewModel.sendEvent(MiWearSettingEvent.SyncMuSicSwitchDialogVisible(true))
            }
        }
    }

    override fun onPause() {
        super.onPause()
        needShowMusciSource = false
    }

    override fun onStop() {
        super.onStop()
        O95Statistic.cpaEvent(
            "Glasses_Settings",
            tip = "1676.0.0.0.43019",
            hasDurationSec = true,
            durationSec = enterTime
        )
    }

    override val contentView: @Composable () -> Unit = {
        val mState = viewModel.mState.collectAsState()
        val deviceState = viewModel.deviceStateLiveData.observeAsState()
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, funs) = createRefs()
            CommonTitleBar(
                getString(R.string.libs_glasses_setting),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            FunList(
                settingState = mState,
                deviceState = deviceState,
                modifier = Modifier.constrainAs(funs) {
                    top.linkTo(titleBar.bottom)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
            )
        }
        BrightDialog(settingState = mState, isChannlSuccess(deviceState), viewModel = viewModel)
        ShootModeDialog(settingState = mState, isChannlSuccess(deviceState), viewModel = viewModel)
        TempControlDialog(settingState = mState, isChannlSuccess(deviceState), viewModel = viewModel)
        RadioDirectionDialog(settingState = mState, isChannlSuccess(deviceState), viewModel = viewModel)
        MusicControlDialog(settingState = mState, isChannlSuccess(deviceState), viewModel = viewModel)
        TTSSwitchDialog(settingState = mState, viewModel = viewModel)
        MusicSwitchDialog(settingState = mState, viewModel = viewModel) {
            PackageHelper.openAppInMarket(LibBaseApplication.instance, it)
        }
        RadioStationSwitchDialog(settingState = mState, viewModel = viewModel) {
            PackageHelper.openAppInMarket(LibBaseApplication.instance, it)
        }
        FullDuplexTimeoutDialog(settingState = mState, viewModel = viewModel)
        VideoDurationDialog(
            settingState = mState,
            isChannlSuccess(deviceState),
            viewModel = viewModel
        )
        NewBindDialog(fragment = this, settingState = mState, viewModel = viewModel)
        OtherDeviceDialog(settingState = mState, viewModel = viewModel)
        LaunchedEffect(viewModel.mEffect) {
            viewModel.mEffect.collect { effect ->
                handelEffect(effect)
            }
        }
        VolumeDialog(settingState = mState, isChannlSuccess(deviceState), viewModel = viewModel)
    }

    @Suppress("TooManyFunctions")
    @Composable
    private fun FunList(
        settingState: State<MiWearSettingState>,
        deviceState: State<O95State?>,
        modifier: Modifier
    ) {
        LazyColumn(
            modifier = modifier
                .fillMaxWidth()
                .fillMaxHeight() // 确保 LazyColumn 有足够的高度来滚动
        ) {
            item {
                LightItem(settingState, deviceState)
            }

            item {
                XiaoAiItem(settingState, deviceState)
            }

            item {
                FilmingItem(settingState, deviceState)
            }
            if (settingState.value.tempControl != Un_Support_Flag &&
                appEnvironment.isMIUI() &&
                MiWearCameraUpgradeChecker.isSupportConnectivity(LibBaseApplication.instance)
            ) {
                item {
                    CameraCollaboration(settingState, deviceState)
                }
            }

            item {
                GeneralItem(settingState, deviceState)
            }

            item {
                OtherItem(deviceState)
            }

            item {
                UnbindItem()
            }
        }
    }

    @Composable
    private fun LightItem(settingState: State<MiWearSettingState>, deviceState: State<O95State?>) {
        VerticalSpaced(height = Dp_28)
        SettingItemDesc(tipResource = R.string.tip_lights_and_reminders)
        TitleArrowDes(
            title = stringResource(id = R.string.tip_lights_brightness),
            description = stringResource(id = R.string.tip_lights_brightness_desc),
            enabled = isChannlSuccess(deviceState),
            arrowDescription = getLightDesc(settingState.value.brightness)
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SyncBrightDialogVisible(true))
            O95Statistic.clickGlassSettingEvent("indicator_light_brightness_item")
        }

        if (settingState.value.volumeValue != 0) {
            TitleArrowDes(
                title = stringResource(id = R.string.tip_lights_brightness_volume),
                description = stringResource(id = R.string.tip_lights_brightness_volume_desc),
                enabled = isChannlSuccess(deviceState),
                arrowDescription = getVolumeDesc(settingState.value.volumeValue),
                guidelineType = GuidelineType.Half
            ) {
                viewModel.sendEvent(MiWearSettingEvent.SyncVolumeSettingCommand(true))
                O95Statistic.clickGlassSettingEvent("indicator_volume_item")
            }
        }

        LineSpace()
    }

    @Composable
    private fun isChannlSuccess(deviceState: State<O95State?>): Boolean {
        return deviceState.value?.deviceState is DeviceState.ChannelSuccess
    }

    @Composable
    private fun getLightDesc(lightModel: Int): String {
        return stringResource(
            id = when (lightModel) {
                BRIGHT_CLOSE -> R.string.libs_close
                BRIGHT_LOW -> R.string.libs_low
                BRIGHT_HIGH -> R.string.libs_high
                BRIGHT_MIDDLE -> R.string.libs_middle
                else -> R.string.libs_empty
            }
        )
    }

    @Composable
    private fun getVolumeDesc(volumeValue: Int): String {
        Timber.d("getVolumeDesc called value:$volumeValue")
        return stringResource(
            id = when (volumeValue) {
                VOLUME_AUTO -> R.string.libs_auto
                VOLUME_HIGH -> R.string.libs_high
                VOLUME_MIDDLE -> R.string.libs_middle
                VOLUME_LOW -> R.string.libs_low
                else -> R.string.libs_empty
            }
        )
    }

    @Composable
    private fun getFullDuplexTimeout(fullDuplexTimeout: Long): String {
        return stringResource(
            id = when (fullDuplexTimeout) {
                DuplexTimeout.TEN_SECOND -> R.string.libs_duplex_timeout_ten_second
                DuplexTimeout.TWEEN_SECOND -> R.string.libs_duplex_timeout_tween_second
                DuplexTimeout.THREEN_SECOND -> R.string.libs_duplex_timeout_threen_second
                else -> R.string.libs_empty
            }
        )
    }

    @Composable
    private fun getRadioStationSourceDesc(source: @MusicSource Int, context: Context): String {
        val defaultDescResId = R.string.libs_music_defalut
        return stringResource(
            id = if (isRadioStationAppAuth(source)) getRadioStationSourceResId(source) else defaultDescResId
        )
    }

    @Composable
    private fun getMusicSourceDesc(musicSource: @MusicSource Int, context: Context): String {
        val defaultDescResId = R.string.libs_music_defalut
        return stringResource(
            id = when (musicSource) {
                DEFAULT -> R.string.libs_music_defalut
                QQ, NET_EASE -> {
                    if (isMusicAppAuth(musicSource, context)) {
                        getMusicSourceResId(musicSource)
                    } else {
                        defaultDescResId
                    }
                }

                else -> R.string.libs_music_defalut
            }
        )
    }

    @Composable
    private fun getBackLocationSourceDesc(context: Context): String {
        val granted = context.checkSelfPermission(
            Manifest.permission.ACCESS_BACKGROUND_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        return stringResource(
            id = when (granted) {
                true -> R.string.libs_back_location_allow
                else -> R.string.libs_back_location_defalut
            }
        )
    }

    private fun isMusicAppAuth(musicSource: @MusicSource Int, context: Context): Boolean {
        val cp = when (musicSource) {
            QQ -> MusicCp.QQ
            NET_EASE -> MusicCp.NET_EASE
            else -> return false
        }
        return MusicApiService.INSTANCE.isAuthSuccess(cp)
    }

    private fun isRadioStationAppAuth(source: @RadioStationSource Int): Boolean {
        val cp = when (source) {
            FM -> RadioStationCp.XMLY
            else -> return false
        }
        return MusicApiService.INSTANCE.isAuthSuccess(cp)
    }

    private fun getMusicSourceResId(musicSource: @MusicSource Int): Int {
        return when (musicSource) {
            QQ -> R.string.libs_music_qq
            NET_EASE -> R.string.libs_music_wy
            else -> R.string.libs_music_defalut
        }
    }

    private fun getRadioStationSourceResId(radioStationSource: @MusicSource Int): Int {
        return R.string.libs_music_fm
    }

    @Suppress("LongMethod")
    @Composable
    private fun XiaoAiItem(
        settingState: State<MiWearSettingState>,
        deviceState: State<O95State?>
    ) {
        SettingItemDesc(tipResource = R.string.tip_xiaoai)
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_voice_wake_up),
            description = stringResource(id = R.string.tip_voice_wake_up_desc),
            checked = settingState.value.voiceWakeup,
            enabled = isChannlSuccess(deviceState = deviceState),
            guidelineType = GuidelineType.Normal,
            margin = Dp_2
        ) {
            O95Statistic.eventV3(
                pair.first,
                pair.second,
                hasFromToKey = true,
                fromToKey = EventCons.EVENT_KEY_VOICE_WAKE_UP,
                fromToValue = String.format(
                    EventCons.EVENT_KEY_FROM_TO,
                    settingState.value.voiceWakeup,
                    it
                )
            )
            viewModel.sendEvent(
                MiWearSettingEvent.SetAIAssistant(
                    voiceWakeup = it,
                    cameraAccess = settingState.value.cameraAccess,
                    collaborationWakeup = settingState.value.collaborationWakeup
                )
            )
        }
        // 新需求移除.
//        TitleSwitchDes(
//            title = stringResource(id = R.string.tip_collaboration_wake_up),
//            description = stringResource(id = R.string.tip_collaboration_wake_up_desc),
//            checked = settingState.value.collaborationWakeup,
//            enabled = isChannlSuccess(deviceState = deviceState),
//            margin = Dp_2
//        ) {
//            viewModel.sendEvent(
//                MiWearSettingEvent.SetAIAssistant(
//                    voiceWakeup = settingState.value.voiceWakeup,
//                    cameraAccess = settingState.value.cameraAccess,
//                    collaborationWakeup = it
//                )
//            )
//        }
//        TitleSwitchDes(
//            title = stringResource(id = R.string.tip_share_image_to_xiaoai),
//            description = stringResource(id = R.string.tip_share_image_to_xiaoai_desc),
//            checked = settingState.value.cameraAccess,
//            enabled = isChannlSuccess(deviceState = deviceState),
//            margin = Dp_2
//        ) {
//            viewModel.sendEvent(
//                MiWearSettingEvent.SetAIAssistant(
//                    voiceWakeup = settingState.value.voiceWakeup,
//                    cameraAccess = it
//                )
//            )
//        }
//        TitleArrowDes(
//            title = stringResource(id = R.string.tip_timbre),
//            description = stringResource(id = R.string.tip_timbre_desc),
//            arrowDescription = stringResource(
//                id = when (settingState.value.curTTSFont) {
//                    MI_TANG -> R.string.libs_item_suger
//                    MO_LI -> R.string.libs_item_jasmine
//                    QIN_CONG -> R.string.libs_item_shallots
//                    PAO_FU -> R.string.libs_item_puffs
//                    else -> R.string.libs_empty
//                }
//            ),
//            enabled = isChannlSuccess(deviceState = deviceState)
//        ) {
//            viewModel.sendEvent(MiWearSettingEvent.SyncTTSSwitchDialogVisible(true))
//        }

        TitleArrowDes(
            title = stringResource(id = R.string.tip_continue_dialogue),
            description = stringResource(id = R.string.tip_continue_dialogue_desc),
            arrowDescription = getFullDuplexTimeout(settingState.value.fullDuplexTimeout),
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SyncFullDuplexTimeoutSwitchDialogVisible(true))
            O95Statistic.clickGlassSettingEvent("continuous_session_duration_item")
        }
        TitleArrowDes(
            title = stringResource(id = R.string.tip_music_source),
            description = stringResource(id = R.string.tip_music_source_desc),
            arrowDescription = getMusicSourceDesc(
                settingState.value.musicSource,
                LibBaseApplication.instance
            ),
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SyncMuSicSwitchDialogVisible(true))
            O95Statistic.clickGlassSettingEvent("music_source_item")
        }
        TitleArrowDes(
            title = stringResource(id = R.string.tip_radio_station_source),
            description = stringResource(id = R.string.tip_radio_station_desc),
            arrowDescription = getRadioStationSourceDesc(
                settingState.value.radioStationSource,
                LibBaseApplication.instance
            ),
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SyncRadioStationSwitchDialogVisible(true))
        }
        TitleArrowDes(
            title = stringResource(id = R.string.tip_back_location_source),
            description = stringResource(id = R.string.tip_back_location_desc),
            arrowDescription = getBackLocationSourceDesc(LibBaseApplication.instance),
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SendBackLocationCommand)
        }
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_smart_family_title),
            description = stringResource(id = R.string.tip_smart_family_desc),
            checked = viewModel.smartFamilyToggle.value,
            enabled = isChannlSuccess(deviceState = deviceState),
            onAllowSwitchChange = {
                if (!NetworkMonitor.isConnected()) {
                    toast(R.string.No_Network)
                    false
                } else {
                    true
                }
            }
        ) { value ->
            viewModel.sendEvent(MiWearSettingEvent.SendSmartFamilyState(value))
        }
//        TitleSwitchDes(
//            title = "音频SBC编码",
//            description = "固件音频是否需要SBC解码",
//            checked = SBCCodec.isSbcDecodeEnable(),
//            enabled = true,
//            margin = Dp_2
//        ) {
//            SBCCodec.setSbcDecodeEnable(it)
//        }
        LineSpace()
    }

    @Suppress("LongMethod", "ComplexMethod")
    @Composable
    private fun FilmingItem(
        settingState: State<MiWearSettingState>,
        deviceState: State<O95State?>
    ) {
        SettingItemDesc(tipResource = R.string.tip_filming)
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_photo_water_mark),
            checked = MMKVUtils.decodeBoolean(ENABLE_WATERMARK),
            description = stringResource(id = R.string.tip_photo_water_mark_des),
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            MMKVUtils.encode(ENABLE_WATERMARK, it)
        }
        TitleArrowDes(
            title = stringResource(id = R.string.tip_video_clip_length),
            description = stringResource(id = R.string.tip_video_clip_length_desc),
            enabled = isChannlSuccess(deviceState = deviceState),
            arrowDescription = stringResource(
                id = when (settingState.value.videoDuration) {
//                    NO_RESTRICTIONS -> R.string.dialog_no_restrictions
                    SECONDS_15 -> R.string.dialog_seconds_15
                    SECONDS_60 -> R.string.dialog_seconds_60
                    SECONDS_120 -> R.string.dialog_seconds_120
                    SECONDS_180 -> R.string.dialog_seconds_180
                    SECONDS_300 -> R.string.dialog_seconds_300
                    SECONDS_600 -> R.string.dialog_seconds_600
                    else -> R.string.libs_empty
                }
            )
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SyncVideoDialogVisible(true))
            O95Statistic.clickGlassSettingEvent("video_duration_item")
        }
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_audo_save_sys_album),
            description = stringResource(id = R.string.tip_audo_save_sys_album_desc),
            checked = settingState.value.autoSyncSystemAlbums,
            enabled = isChannlSuccess(deviceState = deviceState),
            margin = Dp_2
        ) {
            O95Statistic.eventV3(
                pair.first,
                pair.second,
                hasFromToKey = true,
                fromToKey = EventCons.EVENT_KEY_AUTO_SAVE,
                fromToValue = String.format(EventCons.EVENT_KEY_FROM_TO, settingState.value.autoSyncSystemAlbums, it)
            )
            viewModel.sendEvent(MiWearSettingEvent.SetAutoSyncSystemAlbumsState(it))
        }
        if (settingState.value.radioDirection != Un_Support_Flag) {
            TitleArrowDes(
                title = stringResource(id = R.string.tip_radio_direction),
                description = stringResource(id = R.string.tip_radio_direction_desc),
                enabled = isChannlSuccess(deviceState = deviceState),
                guidelineType = GuidelineType.FourTenths,
                arrowDescription = stringResource(
                    id = when (settingState.value.radioDirection) {
                        DIRECTION_ALL -> R.string.tip_dialog_radio_direction_all
                        DIRECTION_FOR_WORD -> R.string.tip_dialog_radio_direction_for_word
                        else -> R.string.libs_empty
                    }
                )
            ) {
                viewModel.sendEvent(MiWearSettingEvent.SyncRadioDirectionDialogVisible(true))
                O95Statistic.clickGlassSettingEvent("get_sound_mode_item")
            }
        }
//        if (settingState.value.tempControl != Un_Support_Flag) {
//            TitleArrowDes(
//                title = stringResource(id = R.string.tip_temp_control),
//                description = stringResource(id = R.string.tip_temp_control_desc),
//                enabled = isChannlSuccess(deviceState = deviceState),
//                guidelineType = GuidelineType.FourTenths,
//                arrowDescription = stringResource(
//                    id = when (settingState.value.tempControl) {
//                        TEMP_NORMAL -> R.string.tip_dialog_temp_control_normal
//                        TEMP_NONE -> R.string.tip_dialog_temp_control_none
//                        else -> R.string.libs_empty
//                    }
//                )
//            ) {
//                viewModel.sendEvent(MiWearSettingEvent.SyncTempControlDialogVisible(true))
//            }
//        }

        if (settingState.value.shieldReminder != Un_Support_Flag) {
            TitleSwitchDes(
                title = stringResource(id = R.string.tip_shield_reminder),
                description = stringResource(id = R.string.tip_shield_reminder_desc),
                checked = settingState.value.shieldReminder == SHIELD_OPEN,
                enabled = isChannlSuccess(deviceState = deviceState),
                margin = Dp_2
            ) {
                viewModel.sendEvent(
                    MiWearSettingEvent.SetMediaSetting(
                        shootingMode = settingState.value.shootingMode,
                        videoDuration = settingState.value.videoDuration,
                        radioDirection = settingState.value.radioDirection,
                        tempControl = settingState.value.tempControl,
                        shieldReminder = if (it) SHIELD_OPEN else SHIELD_CLOSE
                    )
                )
            }
        }

//        TitleArrowDes(
//            title = stringResource(id = R.string.tip_shooting_method),
//            description = stringResource(id = R.string.tip_shooting_method_desc),
//            enabled = isChannlSuccess(deviceState = deviceState),
//            arrowDescription = stringResource(
//                id = if (settingState.value.shootingMode == 1) {
//                    R.string.tip_click_to_take_photo
//                } else {
//                    R.string.tip_click_record
//                }
//            )
//        ) {
//            viewModel.sendEvent(MiWearSettingEvent.SyncShootDialogVisible(true))
//        }
        LineSpace()
    }

    @Composable
    private fun CameraCollaboration(settingState: State<MiWearSettingState>, deviceState: State<O95State?>) {
        SettingItemDesc(tipResource = R.string.libs_camera_collaboration)
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_temp_control),
            checked = settingState.value.tempControl == TEMP_NONE,
            description = stringResource(id = R.string.tip_temp_control_desc),
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            viewModel.sendEvent(MiWearSettingEvent.SyncTempControlState(it))
        }
        LineSpace()
    }

    @Suppress("LongMethod")
    @Composable
    private fun GeneralItem(
        settingState: State<MiWearSettingState>,
        deviceState: State<O95State?>
    ) {
        val wlanState = viewModel.wlanState.collectAsState()
        SettingItemDesc(tipResource = R.string.tip_general)
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_wearing_detection),
            description = stringResource(id = R.string.tip_wearing_detection_desc),
            checked = settingState.value.wearDetection,
            margin = Dp_2,
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            O95Statistic.eventV3(
                pair.first,
                pair.second,
                hasFromToKey = true,
                fromToKey = EventCons.EVENT_KEY_WEAR_DETECTION,
                fromToValue = String.format(
                    EventCons.EVENT_KEY_FROM_TO,
                    settingState.value.wearDetection,
                    it
                )
            )
            viewModel.sendEvent(
                MiWearSettingEvent.SetHeadSet(
                    wearDetection = it,
                    volumeAdaptive = settingState.value.volumeAdaptive,
                    preventSoundLeak = settingState.value.preventSoundLeak,
                    musicControl = settingState.value.musicControl
                )
            )
        }

        TitleSwitchDes(
            title = stringResource(id = R.string.tip_privacy_mode),
            description = stringResource(id = R.string.tip_privacy_mode_desc),
            checked = settingState.value.preventSoundLeak,
            margin = Dp_2,
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            O95Statistic.eventV3(
                pair.first,
                pair.second,
                hasFromToKey = true,
                fromToKey = EventCons.EVENT_KEY_PRIVACY_MODE,
                fromToValue = String.format(
                    EventCons.EVENT_KEY_FROM_TO,
                    settingState.value.preventSoundLeak,
                    it
                )
            )

            viewModel.sendEvent(
                MiWearSettingEvent.SetHeadSet(
                    wearDetection = settingState.value.wearDetection,
                    volumeAdaptive = settingState.value.volumeAdaptive,
                    preventSoundLeak = it,
                    musicControl = settingState.value.musicControl
                )
            )
        }
        if (settingState.value.musicControl != Un_Support_Flag) {
            TitleArrowDes(
                title = stringResource(id = R.string.tip_music_control),
                description = stringResource(id = R.string.tip_music_control_desc),
                enabled = isChannlSuccess(deviceState = deviceState),
                guidelineType = GuidelineType.FourTenths,
                arrowDescription = stringResource(
                    id = when (settingState.value.musicControl) {
                        MUSIC_SINGLE -> R.string.tip_dialog_music_control_single_touch
                        MUSIC_DOUBLE -> R.string.tip_dialog_music_control_double_touch
                        else -> R.string.libs_empty
                    }
                )
            ) {
                viewModel.sendEvent(MiWearSettingEvent.SyncMusicControlDialogVisible(true))
            }
        }
//        TitleSwitchDes(
//            title = stringResource(id = R.string.tip_volume_adaptation),
//            description = stringResource(id = R.string.tip_volume_adaptation_desc),
//            checked = settingState.value.volumeAdaptive,
//            enabled = isChannlSuccess(deviceState = deviceState),
//            margin = Dp_2
//        ) {
//            viewModel.sendEvent(MiWearSettingEvent.SetHeadSet(settingState.value.wearDetection, it))
//        }
        TitleArrowDes(
            title = stringResource(id = R.string.tip_wifi_setting),
            description = stringResource(id = R.string.tip_wifi_setting_desc),
            enabled = isChannlSuccess(deviceState = deviceState)
//            arrowDescription = if (isChannlSuccess(deviceState = deviceState)) wlanState.value else ""
        ) {
            CameraJointDetectionManager.checkIsJointState {
                HexaRouter.Settinng.navigateToWlan(this)
            }
            O95Statistic.clickGlassSettingEvent("wifi_settings_item")
        }
        TitleSwitchDes(
            title = stringResource(id = R.string.tip_silent_upgrade),
            description = stringResource(id = R.string.tip_silent_upgrade_desc),
            checked = if (isChannlSuccess(deviceState = deviceState)) settingState.value.silentUpgrade else false,
            enabled = isChannlSuccess(deviceState = deviceState),
            margin = Dp_2
        ) {
            O95Statistic.eventV3(
                pair.first,
                pair.second,
                hasFromToKey = true,
                fromToKey = EventCons.EVENT_KEY_SILENT_UPGRADE,
                fromToValue = String.format(
                    EventCons.EVENT_KEY_FROM_TO,
                    settingState.value.silentUpgrade,
                    it
                )
            )
            viewModel.sendEvent(MiWearSettingEvent.SetSilentUpgrade(it))
        }
        LineSpace()
    }

    @Suppress("LongMethod")
    @Composable
    private fun OtherItem(deviceState: State<O95State?>) {
        val factoryMode = viewModel.factoryMode.collectAsState()
        SettingItemDesc(tipResource = R.string.libs_others)
        TitleArrowRedDot(
            title = stringResource(id = R.string.tip_device_upgrade),
            arrowDescription = getDeviceVersion(
                deviceState.value?.deviceInfo,
                deviceState.value?.updateInfo
            ),
            showDot = deviceState.value?.updateInfo != null,
            showRed = false,
            enabled = isChannlSuccess(deviceState = deviceState)
        ) {
            O95Statistic.clickGlassSettingEvent("ota_update")
            HexaRouter.AudioGlasses.navigateToOTA(this)
        }
        TitleArrowDes(
            title = stringResource(id = R.string.tip_device_info),
            arrowDescription = ""
        ) {
            O95Statistic.clickGlassSettingEvent("device_info")
            BlueDeviceDbHelper.getBondDeviceByMiWearDid(did ?: "")?.let {
                HexaRouter.AudioGlasses.navigateToDeviceInfo(
                    this,
                    sn = it.sn ?: "",
                    model = it.model ?: ""
                )
            }
        }
        // 法律信息隐藏
//        TitleArrowDes(
//            title = stringResource(id = R.string.lawInfo),
//            arrowDescription = ""
//        ) { HexaRouter.AudioGlasses.navigateToLawInfo(this) }

        TitleArrowDes(
            title = stringResource(id = R.string.libs_connect_new_phone),
            enabled = isChannlSuccess(deviceState = deviceState),
            arrowDescription = ""
        ) {
            O95Statistic.clickGlassSettingEvent("connect_to_new_phone")
            viewModel.sendEvent(MiWearSettingEvent.SwitchNewBindState(NewBindState.StartNewBind))
        }
        if (deviceState.value?.deviceInfo?.firmwareVersion?.isVersionGreaterOrEqual() == true) {
            TitleArrowDes(
                title = stringResource(id = R.string.libs_connect_other_device),
                enabled = isChannlSuccess(deviceState = deviceState),
                arrowDescription = ""
            ) {
                viewModel.sendEvent(MiWearSettingEvent.SyncOtherDeviceDialogVisible(true))
            }
        }

        if (BuildConfig.DEBUG || MMKVUtils.decodeBoolean(DevelopModelOpen, false)) {
            TitleArrowDes(
                title = stringResource(id = R.string.libs_sales_self_inspection),
                arrowDescription = "",
                enabled = isChannlSuccess(deviceState = deviceState)
            ) { HexaRouter.Detection.navigationToDetection(this) }
        }

        if (BuildConfig.DEBUG || MMKVUtils.decodeBoolean(DevelopModelOpen, false)) {
            TitleSwitch(
                title = "开启Dump",
                checked = MMKVUtils.decodeBoolean(ENABLE_DUMP),
                enabled = isChannlSuccess(deviceState = deviceState)
            ) {
                MMKVUtils.encode(ENABLE_DUMP, it)
            }
        }

        if (BuildConfig.DEBUG || MMKVUtils.decodeBoolean(DevelopModelOpen, false)) {
            TitleSwitchDes(
                title = stringResource(id = R.string.libs_auto_report_logs),
                description = stringResource(id = R.string.libs_auto_report_logs_des),
                checked = factoryMode.value == 1,
                enabled = isChannlSuccess(deviceState = deviceState),
                margin = Dp_2
            ) {
                viewModel.sendEvent(MiWearSettingEvent.SetFactoryMode(it))
            }
        }
    }

    @Composable
    private fun getDeviceVersion(
        deviceInfo: SystemProtos.DeviceInfo?,
        updateInfo: MiWearUpgradeInfo?
    ): String {
        val firmwareVersion = deviceInfo?.firmwareVersion
//        return if (updateInfo != null) {
//            stringResource(id = R.string.deviceUpdateHasNewVersion)
//        } else
        return if (firmwareVersion?.isNotEmpty() == true) {
            "V$firmwareVersion"
        } else {
            ""
        }
    }

    @Composable
    private fun UnbindItem() {
        SubmitButton(
            textColor = ColorBlack,
            subTitle = getString(R.string.deviceUnbind),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Dp_28, vertical = Dp_28),
            enable = true
        ) {
            val isMonkey = BuildConfig.MONKEY_PACKAGE
            if (!isMonkey) {
                O95Statistic.clickGlassSettingEvent("unbind_button")
                if (MediaSpaceHandler.isDownloading()) {
                    toast(R.string.libs_file_transfer_please_try_again_later)
                } else {
                    checkIsLowBattery {
                        val isConnected = viewModel.deviceStateLiveData.isSuccessState()
//                    val summaryCount = viewModel.deviceStateLiveData.value?.mediaSummary ?: 0
                        val summaryCount = MediaSpaceHandler.mediaData.value.mediaSummary
                        if (!isConnected) {
                            MiWearUnBindDialogFragment.showUnConnectedTipDialog(this) {
                                showUnBindDialog()
                            }
                        } else if (summaryCount > 0) {
                            MiWearUnBindDialogFragment.showUnTransTipDialog(
                                this
                            ) {
                                showUnBindDialog()
                            }
                        } else {
                            showUnBindDialog()
                        }
                    }
                }
            }
        }
    }

    private fun checkIsLowBattery(callback: () -> Unit) {
        showLoading()
        val event = MiWearSettingEvent.CheckIsLowBattery { lowBattery ->
            hideLoading()
            if (lowBattery) {
                toast(R.string.libs_unbind_low_battery_tip)
            } else {
                callback.invoke()
            }
        }
        viewModel.sendEvent(event)
    }

    private fun showUnBindDialog() {
        MiWearUnBindDialogFragment.showUnBindDialog(
            this@MiWearSettingFragment
        ) {
            viewModel.sendEvent(MiWearSettingEvent.UnBindDevice)
        }
    }

    private fun handelEffect(effect: MiWearSettingEffect) {
        when (effect) {
            is MiWearSettingEffect.UnBindStart -> {
                showUnBindDialogByState(UNBINDING_STATE)
            }

            is MiWearSettingEffect.UnBindFailed -> {
                showUnBindDialogByState(UNBIND_FAILED_STATE)
            }

            is MiWearSettingEffect.UnBindSuccess -> {
                showUnBindDialogByState(UNBIND_SUCCESS_STATE)
                effect.mac?.let {
                    if (DeviceModelManager.isAssociateDevice(o95cnsModel)) {
                        DeviceCompanionManager.INSTANCE.disassociate(requireActivity(), effect.mac)
                    } else {
                        MMANearbyManager.unBindMMANearby(o95cnsModel)
                    }
                }
            }

            is MiWearSettingEffect.ShowTips -> {
            }
        }
    }

    private fun getUnBindingDialog() = MiWearUnBindStateDialogFragment.getInstance(this) {
        navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
        Timber.d("navigator.popTo MainFragment")
    }

    private fun showUnBindDialogByState(state: String) {
        if (state == UNBINDING_STATE) {
            unBindingDialog?.dismiss()
            unBindingDialog = getUnBindingDialog()
            unBindingDialog?.show(this.childFragmentManager, "SSUnBindStateDialog")
        }
        unBindingDialog?.showUnBindStateByType(state)
    }

    @Composable
    private fun VerticalSpaced(height: Dp = Dp_12) {
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(height)
        )
    }

    @Composable
    private fun LineSpace() {
        Divider(
            color = ColorWhite10, // 设置线条颜色
            thickness = Dp_0_5, // 设置线条厚度
            modifier = Modifier
                .fillMaxWidth() // 设置宽度为父容器的宽度
                .height(Dp_41)
                .padding(horizontal = Dp_20, vertical = Dp_20) // 设置两边间距
        )
    }

    companion object {
        const val Un_Support_Flag = 0 // 不支持
    }
}

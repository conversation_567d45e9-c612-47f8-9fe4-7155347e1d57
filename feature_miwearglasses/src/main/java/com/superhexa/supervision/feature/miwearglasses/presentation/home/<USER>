package com.superhexa.supervision.feature.miwearglasses.presentation.home

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95Action
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.GetSystemSettings
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.MiWearDeviceStatusHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.alert.MiWearAlertStatusHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.alert.MiWearAlertStatusHandler.Companion.checkHasOTAData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.media.MiWearMediaHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.ota.MiWearDeviceOTAProgressHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.MiWearRecordHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordStatus
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.xiaomi.fitness.device.contact.export.IDeviceSyncCallback
import com.xiaomi.fitness.device.contact.export.SyncResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/9/5 on 17:03
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions")
class MiWearHomeViewModel : BaseMVIViewModel<MiWearHomeState, MiWearHomeEffect, MiWearHomeEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy {
        if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
        } else {
            null
        }
    }
    private val _isRecording = MutableStateFlow(RecordStatus.RECORD_STOP)
    val isRecording
        get() = _isRecording
    // 目前仅用于断连状态重置
    fun resetRecordStatus() {
        Timber.d("MiWearHomeViewModel 设备断连重置录音状态")
        _isRecording.value = RecordStatus.RECORD_STOP
    }

    private val miWearMediaHandler by lazy { MiWearMediaHandler().bindDecorator(decorator) }
    private val miWearDeviceStatusHandler by lazy { MiWearDeviceStatusHandler().bindDecorator(decorator) }
    private val miWearAlertStatusHandler by lazy { MiWearAlertStatusHandler().bindDecorator(decorator) }
    val deviceStateLiveData = decorator?.liveData
    private val _notEnoughSpaceShowLiveData = MutableLiveData(false)
    val notEnoughSpaceShowLiveData = _notEnoughSpaceShowLiveData.asLiveData()
    private val isEnoughFileSpace: Boolean by lazy { checkAvailableSpace() }

    override fun initUiState() = MiWearHomeState(
        deviceInfo = bondDevice ?: BondDevice(),
        guideItems = fetchGuideItems()
    )

    fun isDisconnected(): Boolean {
        return decorator?.liveData?.value?.deviceState is DeviceState.Disconnected
    }

    fun isChannelSuccess(): Boolean {
        return decorator?.isChannelSuccess() == true
    }

    private fun checkAvailableSpace(): Boolean {
        val leftLength = FileAndDirUtils.leftAvaiableSpace(LibBaseApplication.instance)
        Timber.d("checkAvailableSpace, leftLength:$leftLength")
        val enough = leftLength >= 1f
        _notEnoughSpaceShowLiveData.value = !enough
        return enough
    }

    override fun reduce(oldState: MiWearHomeState, event: MiWearHomeEvent) {
        when (event) {
            is MiWearHomeEvent.InitTempControlState -> getTempControlSettings(oldState)
            is MiWearHomeEvent.ActivelyRefreshEvent -> activelyRefreshState(event.resetNewBindState)
            is MiWearHomeEvent.ClearAlertStatusInfoEvent ->
                decorator?.liveData?.dispatchAction(O95Action.SyncAlertStatus(null))

            is MiWearHomeEvent.CheckCurrentOTAState -> checkOTAState(event.call)
            is MiWearHomeEvent.SyncThumbData -> syncThumbData()
            is MiWearHomeEvent.GetInitialRecordStatus -> getInitialRecordStatus()
            is MiWearHomeEvent.InitRecordObserver -> observeRecordStatus(event.lifecycleOwner)
        }
    }

    @Suppress("MaxLineLength")
    private fun fetchGuideItems(): List<GuideItem> {
        val mutableGuideItem = mutableListOf(
            GuideItem(icon = R.mipmap.ic_guide_photo, title = R.string.libs_basic_guide_more, GuideType.BasicMore, "all_toturial"),
            GuideItem(icon = R.mipmap.ic_guide_recording, title = R.string.libs_extend_guide_more, GuideType.ExtendMore, "all_toturial"),
            GuideItem(icon = R.mipmap.ic_guide_speech, title = R.string.libs_xiaoai_guide_more, GuideType.XiaoAiMore, "all_toturial")
        )
//        if (!DeviceModelManager.isStandardSKU(bondDevice?.sn.orEmpty())) {
//            mutableGuideItem.add(
//                GuideItem(
//                    icon = R.mipmap.ic_adaptive_lenses,
//                    title = if (DeviceModelManager.isMutableSKU(bondDevice?.sn.orEmpty())) {
//                        R.string.libs_adaptive_mutable_lenses
//                    } else {
//                        R.string.libs_adaptive_lenses
//                    },
//                    GuideType.Lenses
//                )
//            )
//        }
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_phone, title = R.string.libs_guide_call, GuideType.Phone)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_recording, title = R.string.libs_guide_recording, GuideType.Recording)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_translate, title = R.string.libs_guide_translate, GuideType.Translate)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_calories, title = R.string.libs_guide_calories, GuideType.Calories)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_speech, title = R.string.libs_guide_ai_speech_basic_voice_interaction, GuideType.Voice)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_speech_baike, title = R.string.libs_guide_ai_speech_item_baike, GuideType.BaiKe)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_speech_music, title = R.string.libs_guide_ai_speech_item_music, GuideType.XiaoAiMusic)
//        )
//        mutableGuideItem.add(
//            GuideItem(icon = R.mipmap.ic_guide_speech_chat, title = R.string.libs_guide_ai_speech_item_chat, GuideType.XiaoAiSearch)
//        )
        return mutableGuideItem
    }

    private fun syncThumbData() = viewModelScope.launch {
        kotlin.runCatching {
            Timber.d("syncThumbData--deviceId:${bondDevice?.deviceId}")
            miWearMediaHandler.syncMediaFileData(bondDevice?.deviceId.toString())
        }.getOrElse {
            Timber.d("syncThumbData--error:${it.printDetail()}")
        }
    }

    private fun checkOTAState(call: () -> Unit) = viewModelScope.launch {
        if (MiWearDeviceOTAProgressHandler.isOtaState()) {
            val alertStatus = miWearAlertStatusHandler.getAlertStatus()
            if (!checkHasOTAData(alertStatus)) {
                call.invoke()
            }
        } else {
            call.invoke()
        }
    }

    private fun activelyRefreshState(resetNewBindState: Boolean) = viewModelScope.launch {
        if (!isEnoughFileSpace) {
            Timber.d("activelyRefreshState 存储空间不足 abort")
            return@launch
        }
        if (decorator?.isChannelSuccess() == true) { // 已连接状态
            miWearDeviceStatusHandler.getDeviceStatus()
        } else {
            bondDevice?.let { device ->
                if (resetNewBindState) {
                    decorator?.resetNewBindState()
                }
                decorator?.reConnect(device)
            }
        }
    }

    private fun getTempControlSettings(oldState: MiWearHomeState) = viewModelScope.launch {
        if (decorator?.isChannelSuccess() == true) {
            decorator?.sendMiWearCommand(
                GetSystemSettings(GetSystemSettings.allSettings),
                object : IDeviceSyncCallback.Stub() {
                    override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                        result?.packet?.system?.systemSetting?.mediaSetting?.tempControl?.let {
                            Timber.d("getTempControlSettings:onSyncSuccess $it")
                            setState(oldState.copy(tempControl = it))
                        }
                    }

                    override fun onSyncError(did: String?, type: Int, code: Int) {
                        Timber.d("getTempControlSettings:onSyncError $code")
                    }
                }
            )
        } else {
            Timber.d("getTempControlSettings:isNotChannelSuccess")
        }
    }

    private fun observeRecordStatus(
        lifecycleOwner: LifecycleOwner
    ) {
        MiWearRecordHandler.reportRecordStatusCallback.observe(lifecycleOwner) { status ->
            Timber.d("MiWearHomeViewModel reportRecordStatus:$status")
            status?.extraData?.let {
                _isRecording.value = it.status
            }
        }
    }

    private fun getInitialRecordStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            Timber.d("MiWearHomeViewModel startGetInitialRecordStatus")
            val recordStatus = MiWearRecordHandler.getRecordStatus(decorator)
            recordStatus?.extraData?.let {
                withContext(Dispatchers.Main) {
                    _isRecording.value = it.status
                }
                Timber.d("MiWearHomeViewModel getInitRecordStatus:$recordStatus")
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        miWearDeviceStatusHandler.releaseDecorator()
        miWearAlertStatusHandler.releaseDecorator()
    }

    fun getBoundDeviceSid(): String = bondDevice?.sid ?: ""
}

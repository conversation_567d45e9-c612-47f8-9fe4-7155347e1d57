package com.superhexa.supervision.feature.miwearglasses.presentation.home

import androidx.annotation.Keep
import androidx.lifecycle.LifecycleOwner
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice

/**
 * 类描述:
 * 创建日期: 2024/9/5 on 17:04
 * 作者: qintaiyuan
 */
@Keep
data class MiWearHomeState(
    val deviceInfo: BondDevice,
    val guideItems: List<GuideItem>,
    val tempControl: Int = 0
) : UiState

@Keep
sealed class MiWearHomeEvent : UiEvent {
    data class CheckCurrentOTAState(val call: () -> Unit) : MiWearHomeEvent()
    data class ActivelyRefreshEvent(val resetNewBindState: Boolean) : MiWearHomeEvent()
    object ClearAlertStatusInfoEvent : MiWearHomeEvent()
    object SyncThumbData : MiWearHomeEvent()
    object InitTempControlState : MiWearHomeEvent()
    object GetInitialRecordStatus : MiWearHomeEvent()
    data class InitRecordObserver(val lifecycleOwner: LifecycleOwner) : MiWearHomeEvent()
}

@Keep
sealed class MiWearHomeEffect : UiEffect {
    data class ShowTips(val msg: String) : MiWearHomeEffect()
}

@Keep
data class FeatureItem(
    val icon: Int,
    val title: Int,
    val statusIcon: Int? = null,
    val showDot: Boolean = false,
    val onClick: () -> Unit = {}
)

@Keep
sealed class GuideType {
    object BasicMore : GuideType()
    object ExtendMore : GuideType()
    object XiaoAiMore : GuideType()
    object Photo : GuideType()
    object Music : GuideType()
    object Lenses : GuideType()
    object Phone : GuideType()
    object Recording : GuideType()
    object Voice : GuideType()
    object BaiKe : GuideType()
    object XiaoAiMusic : GuideType()
    object XiaoAiSearch : GuideType()
    object Translate : GuideType()
    object Calories : GuideType()
}

@Keep
data class GuideItem(
    val icon: Int,
    val title: Int,
    val guideType: GuideType,
    val event: String? = "all_toturial"
)

@file:Suppress("TooGenericExceptionCaught", "ReturnCount")

package com.superhexa.supervision.library.net.retrofit.download

import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.net.retrofit.CoroutineBase
import com.superhexa.supervision.library.net.retrofit.interceptor.TimeOutInterceptor
import com.superhexa.supervision.library.net.retrofit.upload.ProgressRequestBody
import com.superhexa.supervision.library.net.retrofit.upload.UploadProgressListener
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.MultipartBody.Part.Companion.createFormData
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.ResponseBody
import okhttp3.logging.HttpLoggingInterceptor
import okio.Buffer
import okio.BufferedSink
import okio.ForwardingSource
import okio.appendingSink
import okio.buffer
import okio.sink
import retrofit2.Call
import retrofit2.HttpException
import retrofit2.Response
import retrofit2.Retrofit
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.util.concurrent.TimeUnit
import java.util.regex.Pattern

/**
 * 类描述:文件下载的类，
 * 创建日期:2021/8/13 on 8:17 下午
 * 作者: FengPeng
 */
object FileDownloader : CoroutineBase() {

    private const val tag = "FileDownloader"

    // 保存当前的下载Call对象，用于取消操作
    private var currentDownloadCall: Call<ResponseBody>? = null

    private val fileService by lazy {
        serviceBuilder().create(
            FileService::class.java
        )
    }

    private const val readoutTime = 120L
    private const val downloadTime = 500L
    private const val maxDownloadRetryNum = 5

    // 添加取消下载的方法
    fun cancelDownload() {
        currentDownloadCall?.let {
            if (!it.isCanceled) {
                it.cancel()
                Timber.d("$tag : Download canceled")
            }
        }
        currentDownloadCall = null
    }

    private fun serviceBuilder(): Retrofit {
        // --- OkHttp client ---//
        val okHttpClient = OkHttpClient.Builder()
            .readTimeout(readoutTime, TimeUnit.SECONDS)
            .connectTimeout(readoutTime, TimeUnit.SECONDS)
            .writeTimeout(readoutTime, TimeUnit.SECONDS)

        // --- Add authentication headers ---//
        okHttpClient.addInterceptor { chain ->
            val original = chain.request()

            // Just some example headers
            val requestBuilder = original.newBuilder()
                .addHeader("Connection", "keep-alive")
                .header("User-Agent", "downloader")

            val request = requestBuilder.build()
            chain.proceed(request)
        }

        // --- Add logging ---//

        if (BuildConfig.DEBUG) {
            // development build
            val logging = HttpLoggingInterceptor()
            logging.setLevel(HttpLoggingInterceptor.Level.BASIC)
            okHttpClient.addInterceptor(logging)
        }
        // 超时设置拦截器，方便每个请求单独设置
        okHttpClient.addInterceptor(TimeOutInterceptor())

        // --- Return Retrofit class ---//
        return Retrofit.Builder()
            .client(okHttpClient.build())
            .baseUrl(ConstantUrls.BASE_URL)
            .build()
    }

    fun downloadOrResume(
        url: String,
        destination: File,
        headers: HashMap<String, String> = HashMap(),
        onProgress: ((percent: Int, downloaded: Long, total: Long) -> Unit)? = null,
        onException: ((Exception) -> Unit)? = null
    ): Boolean {
        // 取消可能存在的之前的下载
        cancelDownload()

        var startingFrom = 0L
        if (destination.exists() && destination.length() > 0L) {
            startingFrom = destination.length()
            headers["Range"] = "bytes=$startingFrom-"
        }
        Timber.d("$tag : Download starting from $startingFrom - headers: $headers")

        Timber.d("$tag : ---------- downloadFileByUrl: getting response -------------")

        var attempt = 0
        while (attempt < maxDownloadRetryNum) {
            var response: Response<ResponseBody>? = null
            try {
                Timber.d("$tag : url => $url")
                val downloadFileCall = fileService.downloadFile(url, headers)
                // 保存当前下载的Call对象
                currentDownloadCall = downloadFileCall
                response = downloadFileCall.execute()

                // 检查是否已取消
                if (currentDownloadCall?.isCanceled == true) {
                    Timber.d("$tag : Download canceled before handling response")
                    return false
                }

                val success = handleDownloadResponse(response, destination, onProgress)

                // 如果handle成功返回true，表示下载和处理都成功
                if (success) {
                    currentDownloadCall = null
                    return true
                } else {
                    // handle返回false，表示处理失败，但非异常，不重试
                    Timber.e("$tag : Handle download response failed")
                    currentDownloadCall = null
                    return false
                }
            } catch (e: Exception) {
                // 检查是否是取消导致的异常
                if (e is IOException && currentDownloadCall?.isCanceled == true) {
                    Timber.d("$tag : Download was canceled")
                    currentDownloadCall = null
                    return false
                }

                // 捕获所有异常，包括execute和handle中的异常
                Timber.e(e, "$tag : Attempt ${attempt + 1} failed")

                // 关闭响应体，避免资源泄漏
                response?.body()?.close()

                attempt++
                if (attempt >= maxDownloadRetryNum) {
                    Timber.e(e, "$tag : Download failed after $maxDownloadRetryNum attempts")
                    currentDownloadCall = null
                    return false
                } else {
                    Timber.w("$tag : Attempt $attempt after exception: ${e.message}")
                    onException?.invoke(e)
                    try {
                        Thread.sleep(downloadTime)
                    } catch (interrupted: InterruptedException) {
                        Thread.currentThread().interrupt()
                        currentDownloadCall = null
                        return false
                    }
                }
            }
        }

        currentDownloadCall = null
        return false
    }

    fun downloadOrResumeWithTimeOut(
        url: String,
        destination: File,
        headers: HashMap<String, String> = HashMap(),
        timeOutSeconds: Long,
        onProgress: ((percent: Int, downloaded: Long, total: Long) -> Unit)? = null
    ): Boolean {
        // 取消可能存在的之前的下载
        cancelDownload()

        var startingFrom = 0L
        if (destination.exists() && destination.length() > 0L) {
            startingFrom = destination.length()
            headers["Range"] = "bytes=$startingFrom-"
        }
        Timber.d("$tag : Download starting from $startingFrom - headers: $headers")

        Timber.d("$tag : ---------- downloadFileByUrl: getting response -------------")

        var attempt = 0
        while (attempt < maxDownloadRetryNum) {
            var response: Response<ResponseBody>? = null
            try {
                val downloadFileCall =
                    FileServiceFactory.getFileService(timeOutSeconds).downloadFile(url, headers)
                // 保存当前下载的Call对象
                currentDownloadCall = downloadFileCall
                response = downloadFileCall.execute()

                // 检查是否已取消
                if (currentDownloadCall?.isCanceled == true) {
                    Timber.d("$tag : Download canceled before handling response")
                    return false
                }

                val success = handleDownloadResponse(response, destination, onProgress)

                // 如果handle成功返回true，表示下载和处理都成功
                if (success) {
                    currentDownloadCall = null
                    return true
                } else {
                    // handle返回false，表示处理失败，但非异常，不重试
                    Timber.e("$tag : Handle download response failed")
                    currentDownloadCall = null
                    return false
                }
            } catch (e: Exception) {
                // 检查是否是取消导致的异常
                if (e is IOException && currentDownloadCall?.isCanceled == true) {
                    Timber.d("$tag : Download was canceled")
                    currentDownloadCall = null
                    return false
                }

                // 捕获所有异常，包括execute和handle中的异常
                Timber.e(e, "$tag : Attempt ${attempt + 1} failed")

                // 关闭响应体，避免资源泄漏
                response?.body()?.close()

                attempt++
                if (attempt >= maxDownloadRetryNum) {
                    Timber.e(e, "$tag : Download failed after $maxDownloadRetryNum attempts")
                    currentDownloadCall = null
                    return false
                } else {
                    Timber.w("$tag : Attempt $attempt after exception: ${e.message}")
                    try {
                        Thread.sleep(downloadTime)
                    } catch (interrupted: InterruptedException) {
                        Thread.currentThread().interrupt()
                        currentDownloadCall = null
                        return false
                    }
                }
            }
        }

        currentDownloadCall = null
        return false
    }

    private const val hundred = 100

    private const val Http_Continue_Error = 416

    private const val Http_Continue_Success = 206

    private const val third = 3

    @Suppress("ReturnCount", "LongMethod", "MaxLineLength")
    private fun handleDownloadResponse(
        response: Response<ResponseBody>,
        destination: File,
        onProgress: ((percent: Int, downloaded: Long, total: Long) -> Unit)?
    ): Boolean {
        Timber.tag(tag).d("$tag : -- downloadFileByUrl: parsing response! $response")
        Timber.tag(tag).d(
            buildString {
                append("\n")
                append("response:$response \n")
                append("response.headers:${response.headers()} \n")
                append("response.raw:${response.raw()} \n")
                append("response.body.contentLength:${response.body()?.contentLength()} \n  ")
                append("File Downloaded length:${destination.length()}\n")
            }
        )
        var downloadState = false

        var startingByte = 0L
        var endingByte = 0L
        var totalBytes = 0L

        if (!response.isSuccessful) {
            // 文件已经下载完毕，请求头又加了下载起始范围，导致返回不对
            if (response.code() == Http_Continue_Error) {
                Timber.tag(tag).d("response.code() == Http_Continue_Error")
                // 已经下载完成
                onProgress?.invoke(hundred, destination.length(), destination.length())
                return true
            } else {
                Timber.tag(tag).d("response is  not  Successful response code %s", response.code())
                throw HttpException(response)
            }
        }

        val contentLength = response.raw().body?.contentLength() ?: 0L

        if (contentLength == 0L) {
            // 已经下载完成
            Timber.tag(tag).d("contentLength==0L")
            onProgress?.invoke(hundred, destination.length(), destination.length())
            return true
        }
        if (response.code() == Http_Continue_Success) {
            Timber.tag(tag).d("$tag : - http 206: Continue download")
            val matcher = Pattern.compile("bytes ([0-9]*)-([0-9]*)/([0-9]*)")
                .matcher(response.headers().get("Content-Range"))
            if (matcher.find()) {
                startingByte = matcher.group(1).toLong()
                endingByte = matcher.group(2).toLong()
                totalBytes = matcher.group(third).toLong()
            }
            Timber.tag(tag).d("$tag : Getting range from $startingByte to $endingByte of $totalBytes bytes")
        } else {
            Timber.tag(tag).d("$tag : - new download")
            endingByte = contentLength
            totalBytes = contentLength
            if (destination.exists()) {
                // 如果响应code == 200  文件存在并且大小等于 contentLength 表明已经下载完成。
                if (contentLength == destination.length() && contentLength > 0) {
                    // 已经下载完成
                    onProgress?.invoke(hundred, destination.length(), destination.length())
                    return true
                } else {
                    // 如果响应code == 200  并且大小不等于 contentLength 删掉来一个新的下载
                    destination.delete()
                }
            }
        }

        Timber.tag(tag).d("$tag : Getting range from $startingByte to $endingByte of $totalBytes bytes")
        val sink: BufferedSink = if (startingByte > 0) {
            destination.appendingSink().buffer()
        } else {
            destination.sink().buffer()
        }

        var lastPercentage = -1
        var totalRead = startingByte
        try {
            sink.use {
                it.writeAll(object : ForwardingSource(response.body()!!.source()) {

                    override fun read(sink: Buffer, byteCount: Long): Long {
                        // 检查是否已取消，如果已取消则停止读取
                        if (currentDownloadCall?.isCanceled == true) {
                            return -1 // 返回-1表示读取结束
                        }

                        val bytesRead = super.read(sink, byteCount)

                        if (bytesRead != -1L) {
                            totalRead += bytesRead

                            val currentPercentage = (totalRead * hundred / totalBytes).toInt()
                            if (currentPercentage > lastPercentage) {
                                lastPercentage = currentPercentage
                                if (lastPercentage != hundred) {
                                    Timber.tag(tag).d("$tag : 进度回调: $currentPercentage - totalRead：$totalRead")
                                    onProgress?.invoke(currentPercentage, totalRead, totalBytes)
                                }
                            }
                        }
                        return bytesRead
                    }
                })
            }

            // 如果是正常完成而不是取消，才通知100%
            if (lastPercentage == hundred && currentDownloadCall?.isCanceled == false) {
                Timber.tag(tag).d("$tag : 进度回调:等待IO流完成，才输出100%")
                onProgress?.invoke(hundred, totalRead, totalBytes)
            }
            downloadState = currentDownloadCall?.isCanceled == true
        } finally {
            response.body()?.close()
        }

        Timber.tag(tag).d("$tag : --- Download complete!")
        return downloadState
    }

    fun checkOtaStatus(url: String, map: Map<String, String>): String {
        val call = fileService.checkOtaStatus(url, map)
        val response = call.execute()
        if (response.isSuccessful) {
            val tmp = response.body() as ResponseBody
            val ret = tmp.string()
            Timber.d("response %s", ret)
            return ret
        } else {
            throw IllegalStateException("response error")
        }
    }

    fun getLogDownloadPath(url: String): String {
        return try {
            val requestMap = mutableMapOf<String, String>()
            requestMap["ConnectTimeout"] = "600"
            requestMap["ReadTimeout"] = "600"
            requestMap["WriteTimeout"] = "600"
            requestMap["Connection"] = "close"
            val call = fileService.getLogDownloadPath(url, requestMap)
            val response = call.execute()
            if (response.isSuccessful) {
                val tmp = response.body() as ResponseBody
                val ret = tmp.string()
                Timber.d("response %s", ret)
                ret
            } else {
                Timber.d("response error, isNotSuccessful")
                throw IllegalStateException("response error")
            }
        } catch (e: Exception) {
            Timber.d("response error:${e.message}")
            throw IllegalStateException("response error")
        }
    }

    fun uploadFile(
        url: String,
        map: Map<String, String>,
        file: File,
        uploadProgressListener: UploadProgressListener? = null
    ): String {
        val call = fileService.uploadFile(
            url,
            map,
            getUploadRequestBody(file, uploadProgressListener)
        )
        val response = call.execute()
        if (response.isSuccessful) {
            val tmp = response.body() as ResponseBody
            val ret = tmp.string()
            Timber.d("response %s", ret)
            return ret
        } else {
            throw IllegalStateException("response error")
        }
    }

    private fun prepareFilePartWithProgress(
        partName: String,
        file: File,
        uploadProgressListener: UploadProgressListener?
    ): MultipartBody.Part? {
        return try {
            val requestBody = file.asRequestBody("application/octet-stream".toMediaTypeOrNull())
            val part: MultipartBody.Part = createFormData(
                partName,
                file.name,
                ProgressRequestBody(requestBody, uploadProgressListener)
            )
            part
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun getUploadRequestBody(
        file: File,
        uploadProgressListener: UploadProgressListener?
    ): RequestBody {
        val mediaType = "application/octet-stream".toMediaTypeOrNull()
        val fileRequestBody = file.asRequestBody(mediaType)
        return ProgressRequestBody(fileRequestBody, uploadProgressListener)
    }
}


package com.xiaomi.aivs.track

import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.engine.listener.IEventTrack
import timber.log.Timber

object EventTrack : IEventTrack {
    private val eventTrackDeque = mutableMapOf<String, MutableMap<String, Any>>()

    // 缓存参数,保存Dialog未开始的数据.
    private var tempParams = mutableMapOf<String, Any>()

    override fun onEventTrackTime(dialogId: String?, key: EventTrackKv, forceUpdate: Boolean) {
        val isHasEvent = isHasEvent(dialogId, pointKey(key))
//        Timber.d("onEventTrackTime:$key,$isHasEvent,$forceUpdate")
        if (!isHasEvent) {
            onEventTrack(dialogId, key, "${System.currentTimeMillis()}")
        }
    }

    override fun onEventIncrease(dialogId: String?, key: EventTrackKv) {
        var count = dialogId?.let {
            currentParams(dialogId).getOrDefault(pointKey(key), 0)
        } ?: run { tempParams.getOrDefault(pointKey(key), 0) }
//        Timber.d("onEventIncrease:$key,${count is Int},$count")
        if (count is Int) {
            onEventTrack(dialogId, key, ++count)
        }
    }

    override fun onEventTrack(dialogId: String?, key: EventTrackKv, value: Any) {
        Timber.d("onEventTrack:$key,$value")
        dialogId?.let {
            currentParams(dialogId)[pointKey(key)] = value
        } ?: run {
            appendTempParams(pointKey(key), value)
        }
    }

    override fun onEventError(
        dialogId: String?,
        key: EventTrackKv,
        type: String,
        errorCode: Int,
        errorMsg: String
    ) {
        Timber.d("onEventError:$key,$type,$errorCode,$errorMsg")
        val error = Triple(type, errorCode, errorMsg)
        onEventTrack(dialogId, key, error.toJson())
    }

    fun doEventTrack() {
        Timber.d("doEventTrack:${eventTrackDeque.size}")
        eventTrackDeque.keys.filter { !it.isNullOrBlank() }.onEach { doEventTrack(it) }
        eventTrackDeque.clear()
    }

    fun trackImageQAEvent(
        eventName: String = EventTrackEvent.EXECUTE.point(),
        tip: String = UploadImageQAEventParams.EVENT_IMAGE_UPLOAD_TIP,
        uploadRequestId: String,
        uploadEventId: String,
        uploadResult: String
    ) {
        val eventMap = mutableMapOf<String, Any>().apply {
            this[UploadImageQAEventParams.EVENT_KEY_TIP] = tip
            this[UploadImageQAEventParams.EVENT_IMAGE_UPLOAD_REQUEST_ID] = uploadRequestId
            this[UploadImageQAEventParams.EVENT_IMAGE_UPLOAD_EVENT_ID] = uploadEventId
            this[UploadImageQAEventParams.EVENT_IMAGE_UPLOAD_RESULT] = uploadResult
        }

        AiSpeechEngine.INSTANCE.onSpeechEventTrack(eventName, eventMap)
    }

    fun trackCrossDeviceControlEvent(
        eventName: String = EventTrackEvent.EXECUTE.point(),
        tip: String = CrossDeviceControlEventParams.EVENT_CROSS_DEVICE_CONTROL_TIP,
        activityType: String,
        startTime: Long = System.currentTimeMillis(),
        traceId: String?
    ) {
        val eventMap = mutableMapOf<String, Any>().apply {
            this[CrossDeviceControlEventParams.EVENT_KEY_TIP] = tip
            this[CrossDeviceControlEventParams.ACTIVITY_TYPE] = activityType
            this[CrossDeviceControlEventParams.START_TIME] = startTime
            this[CrossDeviceControlEventParams.TRACE_ID] = traceId ?: ""
        }

        AiSpeechEngine.INSTANCE.onSpeechEventTrack(eventName, eventMap)
    }

    /**
     * 反馈上传日志相关埋点
     */
    fun trackFeedBackLogEvent(
        eventName: String = EventTrackEvent.EXECUTE.point(),
        tip: String = FeedBackLogEventParams.EVENT_FEED_BAK_LOG_TIP,
        activityType: String,
        requestId: String,
        from: String
    ) {
        val eventMap = mutableMapOf<String, Any>().apply {
            this[FeedBackLogEventParams.EVENT_KEY_TIP] = tip
            this[FeedBackLogEventParams.EVENT_FEED_BAK_LOG_ACTIVITY_TYPE] = activityType
            this[FeedBackLogEventParams.EVENT_FEED_BAK_LOG_REQUEST_ID] = requestId
            this[FeedBackLogEventParams.EVENT_FEED_BAK_LOG_START_TIME] = System.currentTimeMillis()
            this[FeedBackLogEventParams.EVENT_FEED_BAK_LOG_FROM] = from
        }
        AiSpeechEngine.INSTANCE.onSpeechEventTrack(eventName, eventMap)
    }

    /**
     * 同传翻译相关埋点
     */
    fun trackRecordTranslateEvent(
        eventName: String = EventTrackEvent.EXECUTE.point(),
        tip: String = RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_TIP,
        scenarioType: Int,
        activityType: String,
        abnormalStopsNum: Int,
        abnormalStopsReason: String,
        duration: Long,
        endType: Int
    ) {
        val eventMap = mutableMapOf<String, Any>().apply {
            this[RecordTranslateEventParams.EVENT_KEY_TIP] = tip
            this[RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_SCENARIO_TYPE] = scenarioType
            this[RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_ACTIVITY_TYPE] = activityType
            this[RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_ABNORMAL_STOPS_NUM] = abnormalStopsNum
            this[RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_ABNORMAL_STOPS_REASON] = abnormalStopsReason
            this[RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_DURATION] = duration
            this[RecordTranslateEventParams.EVENT_RECORD_TRANSLATE_END_TYPE] = endType
        }
        AiSpeechEngine.INSTANCE.onSpeechEventTrack(eventName, eventMap)
    }

    private fun doEventTrack(dialogId: String?) {
        Timber.d("doEventTrack:$dialogId")
        if (dialogId.isNullOrBlank()) {
            Timber.d("doEventTrack 空 dialogId，跳过上报")
            return
        }

        val eventMap = currentParams(dialogId)
        // 检查是否包含 STATE_REQUEST_ID 或者 STATE_EXEC_RESULT 为空
        val hasRequestId = eventMap.containsKey(pointKey(EventTrackKv.STATE_REQUEST_ID))
        val execResult = eventMap[pointKey(EventTrackKv.STATE_EXEC_RESULT)]

        if (hasRequestId.toString().isBlank() || execResult.toString().isBlank()) {
            // 打印并清理 map
            Timber.e("无效事件参数，跳过上报并清理: $eventMap")
            eventTrackDeque.remove(dialogId)
            return
        }

        dialogId?.let {
            AiSpeechEngine.INSTANCE
                .onSpeechEventTrack(EventTrackEvent.EXECUTE.point(), currentParams(dialogId))
        }
    }

    private fun pointKey(key: EventTrackKv): String = key.point()

    private fun currentParams(dialogId: String): MutableMap<String, Any> {
        return eventTrackDeque.getOrElse(dialogId) { createParams(dialogId) }
    }

    private fun createParams(dialogId: String): MutableMap<String, Any> {
        return mutableMapOf<String, Any>().apply {
            putAll(tempParams)
            tempParams.clear()
            put(EventTrackKv.STATE_QUERY_TYPE.point(), "speak")
            put(EventTrackKv.STATE_RESULT_TYPE.point(), "success")
            put(EventTrackKv.STATE_DUPLEX.point(), true)
            eventTrackDeque[dialogId] = this
        }
    }

    private fun isHasEvent(dialogId: String?, key: String): Boolean {
        return dialogId?.let {
            currentParams(dialogId).containsKey(key)
        } ?: run {
            tempParams.containsKey(key)
        }
    }

    private fun appendTempParams(key: String, value: Any) {
        Timber.d("appendTempParams:$key,$value")
        tempParams[key] = value
    }
}

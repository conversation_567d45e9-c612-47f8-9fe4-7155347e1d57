@file:Suppress("ComplexMeth<PERSON>", "TooGenericExceptionCaught", "MagicNumber")

package com.xiaomi.aivs.engine.process

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import com.google.gson.Gson
import com.superhexa.music.MusicApiService
import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.xiaomi.ai.api.AIApiConstants
import com.xiaomi.ai.api.Application
import com.xiaomi.ai.api.AudioPlayer
import com.xiaomi.ai.api.Dialog
import com.xiaomi.ai.api.Execution
import com.xiaomi.ai.api.FullScreenTemplate
import com.xiaomi.ai.api.FullScreenTemplate.StreamDialogType
import com.xiaomi.ai.api.General
import com.xiaomi.ai.api.MultiModal
import com.xiaomi.ai.api.Nlp
import com.xiaomi.ai.api.Offline.CloudStop
import com.xiaomi.ai.api.PlaybackController
import com.xiaomi.ai.api.Speaker
import com.xiaomi.ai.api.SpeechRecognizer
import com.xiaomi.ai.api.SpeechSynthesizer
import com.xiaomi.ai.api.Sys
import com.xiaomi.ai.api.Template
import com.xiaomi.ai.api.UIController
import com.xiaomi.ai.api.WearableController
import com.xiaomi.ai.api.common.APIUtils
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.ai.api.common.InstructionHeader
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.R
import com.xiaomi.aivs.bridge.PhoneBridge
import com.xiaomi.aivs.data.DialogNode
import com.xiaomi.aivs.data.InstructionConst
import com.xiaomi.aivs.data.StreamType
import com.xiaomi.aivs.engine.event.DeviceEvent
import com.xiaomi.aivs.engine.process.stream.StreamProcessCallback
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl.Companion.TtsStopOptions
import com.xiaomi.aivs.engine.state.AudioFocusState
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.aivs.track.UnResponseInfo
import com.xiaomi.aivs.utils.ExtensionFun.getOrNull
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber
import java.util.Base64
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 * (非大模型)指令解析-process.
 */
@Suppress("LargeClass")
class InstructionProcessor(
    private val context: Context,
    private val phoneBridge: PhoneBridge,
    private val callback: StreamProcessCallback
) : IProcessor {
    // region 核心数据结构
    private data class SessionStats(
        val dialogId: String?,
        var success: Int = 0,
        var failed: Int = 0,
        var filtered: Int = 0,
        var timeoutJob: Job? = null
    )

    private sealed class ProcessResult {
        object Success : ProcessResult()
        object Failed : ProcessResult()
        object Filtered : ProcessResult()
    }

    private val timeoutTime = 60_000L
    private val statsMap = ConcurrentHashMap<String?, SessionStats>()
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val statsLock = Any()
    // endregion

    private var dialogIsIllegal = false
    private val streamStr = StringBuilder()
    private val displayText = StringBuilder()
    private var curAsr: String? = null
    private var queryAsr: String? = null

    // 存储Nlp.StartAnswer-> Nlp.FinishAnswer 之间的指令.
    private var instructions = mutableListOf<Instruction<*>?>()

    private var lastDialogId: String? = null

    private var streamCardConfig: Triple<String, String, String>? = null
    private var streamPageConfig: Triple<String, String, String?>? = null

    private var uploadImageDialogId: MutableList<String> = mutableListOf()
    private var lLMRichContentCache: Instruction<*>? = null
    private var toastStreamCache: Instruction<*>? = null

    private var streamInterrupt = false

    // region 统计和生命周期管理
    private fun startTracking(dialogId: String?) {
        dialogIllegalStatusChange(false, dialogId)
        lastDialogId?.let { did ->
            if (statsMap.contains(did) && lastDialogId != dialogId) {
                endTracking(did)
                EventTrack.onEventTrack(
                    dialogId = did,
                    key = EventTrackKv.STATE_CANCEL_MSG,
                    value = CANCEL_MSG_RESTART
                )
                statsMap.remove(did)?.let { reportStats(it, false) }
            }
        }
        lastDialogId = dialogId
        synchronized(statsLock) {
            Timber.tag(TAG).w("startTracking dialogId = $dialogId")
            statsMap[dialogId]?.timeoutJob?.cancel()
            statsMap[dialogId] = SessionStats(dialogId).apply {
                timeoutJob = scope.launch {
                    delay(timeoutTime)
                    synchronized(statsLock) {
                        recordResult(dialogId, ProcessResult.Failed)
                        statsMap.remove(dialogId)?.let { reportStats(it, true) }
                    }
                }
            }
        }
    }

    private fun endTracking(dialogId: String?) {
        synchronized(statsLock) {
            Timber.tag(TAG).w("endTracking dialogId = $dialogId")
            statsMap.remove(dialogId)?.let {
                it.timeoutJob?.cancel()
                reportStats(it, false)
            }
        }
    }

    private fun recordResult(dialogId: String?, result: ProcessResult) {
        synchronized(statsLock) {
            when (result) {
                ProcessResult.Success -> statsMap[dialogId]?.success =
                    statsMap[dialogId]?.success?.plus(1) ?: 0

                ProcessResult.Failed -> statsMap[dialogId]?.failed =
                    statsMap[dialogId]?.failed?.plus(1) ?: 0

                ProcessResult.Filtered -> statsMap[dialogId]?.filtered =
                    statsMap[dialogId]?.filtered?.plus(1) ?: 0
            }
        }
    }

    private fun reportStats(session: SessionStats, isTimeout: Boolean) {
        with(session) {
            EventTrack.onEventTrack(dialogId, EventTrackKv.STATE_EXEC_INS_SUCCESS, success)
            EventTrack.onEventTrack(dialogId, EventTrackKv.STATE_EXEC_INS_FAILED, failed)
            EventTrack.onEventTrack(dialogId, EventTrackKv.STATE_EXEC_INS_FILTERED, filtered)
            val allOpResult = calculateAllOpResult(session)
            EventTrack.onEventTrack(dialogId, EventTrackKv.STATE_EXEC_RESULT, allOpResult)
            if (isTimeout) {
                EventTrack.onEventTrack(
                    dialogId = dialogId,
                    key = EventTrackKv.UNRESPONSE_INFO,
                    value = Gson().toJson(UnResponseInfo(timeout_time = System.currentTimeMillis()))
                )
            }
            EventTrack.doEventTrack()
            Timber.tag(TAG).w(
                "reportStats 指令统计超时 $isTimeout ,session $session ," +
                    "执行结果 $allOpResult"
            )
        }
    }

    private fun calculateAllOpResult(session: SessionStats): String {
        return when {
            session.failed > 0 -> OP_TRACE_RESULT_FAIL
            session.success == 0 && session.failed == 0 && session.filtered > 0 -> OP_TRACE_RESULT_CANCEL
            session.success > 0 && session.failed == 0 -> OP_TRACE_RESULT_SUCCESS
            else -> OP_TRACE_RESULT_CANCEL
        }
    }

    private fun dialogIllegalStatusChange(value: Boolean, dialogId: String?) {
        Timber.tag(TAG).i("dialogIllegalStatusChange $value,$dialogId")
        dialogIsIllegal = value
    }

    override fun process(
        instruction: Instruction<*>?,
        isStream: Boolean,
        engineType:
            SpeechEngineProxyImpl.Companion.EngineType
    ) {
        if (instruction == null || instruction.header == null) {
            Timber.tag(TAG).w("process Filtered: instruction = $instruction")
            recordResult(null, ProcessResult.Filtered)
            return
        }

        val header = instruction.header
        val dialogId = header.dialogId.getOrNull()
        EventTrack.onEventIncrease(
            dialogId = dialogId,
            key = EventTrackKv.STATE_EXEC_INS_TOTAL
        )
        if (!isStream) { // 只统计非大模型的的指令集.
            instructions.add(instruction)
        }
        try {
            when (header.namespace) {
                AIApiConstants.System.NAME -> processSys(header, dialogId, instruction)
                AIApiConstants.Nlp.NAME -> processNlp(header, dialogId, instruction, isStream, engineType)
                AIApiConstants.Dialog.NAME -> processDialog(header, dialogId, instruction)
                AIApiConstants.Execution.NAME -> TtsDependency.processDependencyTask(dialogId) {
                    processExecution(header, dialogId, instruction)
                }

                AIApiConstants.MultiModal.NAME -> processMultiModal(header, dialogId, instruction)
                AIApiConstants.SpeechRecognizer.NAME -> processSpeechRecognizer(
                    header,
                    dialogId,
                    instruction
                )

                AIApiConstants.SpeechSynthesizer.NAME -> processSpeechSynthesizer(
                    header,
                    dialogId,
                    instruction,
                    isStream
                )

                AIApiConstants.AudioPlayer.NAME -> {
                    TtsDependency.processDependencyTask(dialogId) {
                        processAudioPlayer(header, dialogId, instruction)
                    }
                }

                AIApiConstants.PlaybackController.NAME -> {
                    TtsDependency.processDependencyTask(dialogId) {
                        processPlaybackController(header, dialogId, instruction)
                    }
                }

                AIApiConstants.WearableController.NAME -> processWearableController(
                    header,
                    dialogId,
                    instruction
                )

                AIApiConstants.Template.NAME -> processTemplate(header, dialogId, instruction)
                AIApiConstants.Speaker.NAME -> processSpeaker(header, dialogId, instruction)
                AIApiConstants.General.NAME -> processGeneral(header, dialogId, instruction)
                AIApiConstants.Offline.NAME -> processOffline(header, dialogId, instruction)
                AIApiConstants.Application.NAME -> processAlipay(header, dialogId, instruction)

                else -> processOther(header, dialogId, instruction)
            }
        } catch (e: Exception) {
            handleProcessError(e, dialogId, header)
        }
    }

    private fun handleProcessError(e: Exception, dialogId: String?, header: InstructionHeader?) {
        recordResult(dialogId, ProcessResult.Failed)
        EventTrack.onEventTrack(
            dialogId = dialogId,
            key = EventTrackKv.UNRESPONSE_INFO,
            value = Gson().toJson(UnResponseInfo(System.currentTimeMillis(), e.toString()))
        )
        Timber.tag(TAG).e(e, "指令处理异常: ${header?.fullName}")
    }

    /**
     * - 非大模型NLP指令下发：StartAnswer -> 语义指令 -> FinishAnswer
     *   - InstructionCapability#process()
     * - 大模型NLP指令下发：StartPreStream -> 语义指令 -> FnishPreStream -> StartStream -> 语义指令 -> FinishStream
     *   - LargeModelCapability#onStreamInstruction()
     */
    @SuppressLint("ImplicitSamInstance")
    @Suppress("LongMethod")
    private fun processNlp(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?,
        isStream: Boolean,
        engineType: SpeechEngineProxyImpl.Companion.EngineType
    ) {
        Timber.tag(TAG).d("processNlp:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is Nlp.StartAnswer -> {
                Timber.d("普通指令开始处理.")
                startTracking(dialogId)
                instructions.clear()
                instructions.add(instruction)
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.NLP_START_ANSWER
                )
                TtsDependency.resetTtsTtsSpeechFlag(header?.fullName)
            }

            is Nlp.FinishAnswer -> {
                Timber.d("普通指令处理结束.")
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.NLP_FINISH_ANSWER
                )
                onNlpFinishAnswer(dialogId, header, instructions.toList())
                onDialogIllegal(instruction)
            }

            is Nlp.StartPreStream -> {
                Timber.d("大模型指令开始处理.")
                startTracking(dialogId)
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.START_PRE_STREAM_TIME
                )
                // 大模型loading过程中如果有tts播放则打断
                // 如果处于支付宝退出过程中不打断，避免打断支付宝的退出话术播报
                if (AiSpeechEngine.INSTANCE.isTtsSpeaking() && !isAlipayStatus()) {
                    AiSpeechEngine.INSTANCE.stopTts(
                        stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                            calledFrom = "enterNlp",
                            needResumeMediaPlayer = null,
                            needStopMediaPlayer = null,
                            stopReason = null
                        )
                    )
                }
                AiSpeechEngine.INSTANCE.onNlpEnter(dialogId, header?.fullName, true)
                // EngineStateMachine.onDialogNode(DialogNode.NLP_WAIT)
                if (engineType == SpeechEngineProxyImpl.Companion.EngineType.IMAGE) {
                    // 节省眼镜资源优化点：传图引擎不播放大模型等待音
                    EngineStateMachine.onDialogNode(DialogNode.NLP_WAIT, false)
                } else if (!isAlipayStatus()) {
                    // 支付宝支付阶段不播放大模型等待音
                    EngineStateMachine.onDialogNode(DialogNode.NLP_WAIT, true)
                }
                TtsDependency.resetTtsTtsSpeechFlag(header?.fullName)
                AudioFocusState.doFocusRequest(
                    AiSpeechEngine.INSTANCE.appContext,
                    "Nlp.StartPreStream"
                )
            }

            is Nlp.FinishPreStream -> {
                Timber.d("大模型指令处理结束.")
            }

            is Nlp.StartStream -> {
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.START_STREAM_TIME
                )
                if (isStream) {
                    toastStreamCache = null
                    streamStr.clear()
                    displayText.clear()
                }
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
            }

            is Nlp.FinishStream -> {
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.NLP_FINISH_STREAM
                )
                AiSpeechEngine.INSTANCE.onNlpExit(dialogId, header?.fullName, isStream)
                onDialogIllegal(instruction)
                if (isStream) {
                    Timber.tag(TAG).d(
                        "大模型TTS播报内容:$dialogIsIllegal," +
                            "$streamInterrupt,$dialogId,$streamStr,$displayText"
                    )
                    val markdownTextStr = if (streamInterrupt) {
                        streamInterrupt = false
                        Base64.getEncoder().encodeToString(
                            displayText.append("[已终止]").toString().toByteArray(Charsets.UTF_8)
                        )
                    } else {
                        Base64.getEncoder().encodeToString(
                            displayText.toString().toByteArray(Charsets.UTF_8)
                        )
                    }
                    displayText.toString().takeIf { it.isNotEmpty() }?.let {
                        // markdownText中可能有特殊符号 toString后网页端可能解析不出来，改为Base64
                        toastStreamCache?.let { instruction ->
                            if (instruction.payload is Template.ToastStream) {
                                (instruction.payload as Template.ToastStream).markdownText =
                                    markdownTextStr
                            }
                            AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                                header?.transactionId?.getOrNull(),
                                dialogId,
                                result = it,
                                !dialogIsIllegal,
                                instructionJson = instruction.toString()
                            )
                        } ?: run {
                            dialogId?.let {
                                val abortInstruction =
                                    AiSpeechEngine.INSTANCE.generateToastInstruction(
                                        tts = "[已终止]",
                                        "mockId",
                                        dialogId
                                    )
                                AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                                    header?.transactionId?.getOrNull(),
                                    dialogId,
                                    result = displayText.toString(),
                                    !dialogIsIllegal,
                                    instructionJson = abortInstruction.toString()
                                )
                            }
                        }
                        toastStreamCache = null
                    } ?: run {
                        // 规避连续对话首轮无TTS不释放音频焦点.
                        if (EngineStateMachine.isContinuousDialog() && AiSpeechEngine.INSTANCE.isFirstDialogRound()) {
                            AudioFocusState.doFocusAbandon(
                                context = AiSpeechEngine.INSTANCE.appContext,
                                reason = "Nlp.FinishStream tts is empty."
                            )
                        }
                    }
                }
            }

            is Nlp.LargeLanguageModelContent -> {
                AiSpeechEngine.INSTANCE.onResponseBottomExplain(
                    header?.transactionId?.getOrNull(),
                    dialogId,
                    payload.bottomExplain.getOrNull(),
                    instructionJson = instruction.toString()
                )
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
            }

            else -> {
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
            }
        }
    }

    /**
     * 校验 Nlp.StartAnswer-> Nlp.FinishAnswer 之间的指令，是否包含有效的指令.
     */
    private fun onNlpFinishAnswer(
        dialogId: String?,
        header: InstructionHeader?,
        instructions: List<Instruction<*>?>
    ) {
        Timber.tag(TAG).d("onNlpFinishAnswer:${instructions.size}")
        if (instructions.size <= MIN_NLP_CMD_COUNT) {
            return
        }

        instructions.onEach { instruction ->
            Timber.tag(TAG).d("handleNlpCmdValid instruction:${instruction?.fullName}")
            if (AIApiConstants.Dialog.Reject.equals(instruction?.fullName, true)) {
                Timber.tag(TAG).w("it has ${AIApiConstants.Dialog.Reject}")
                return
            }
        }

        // 规避连续对话首轮无TTS不释放音频焦点.
        if (!TtsDependency.hasTtsSpeech() && AiSpeechEngine.INSTANCE.isFirstDialogRound()) {
            AudioFocusState.doFocusAbandon(
                context = AiSpeechEngine.INSTANCE.appContext,
                reason = "Nlp.FinishStream tts is empty."
            )
        }

        // TODO 收到正常的nlp指令（排除globalContext的 sys.ack指令).
        AiSpeechEngine.INSTANCE.onNlpEnter(dialogId, header?.fullName, false)
    }

    fun isNormalInstructions(instructions: Instruction<*>?): Boolean {
        return !AIApiConstants.Dialog.Reject.equals(instructions?.fullName, true)
    }

    private fun processDialog(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processDialog:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is Dialog.Finish -> {
                // EventTrack.doEventTrack(dialogId)
                endTracking(dialogId)
            }

            is Dialog.Reject -> {
                EventTrack.onEventTrackTime(
                    dialogId = instruction.header?.dialogId?.getOrNull(),
                    key = EventTrackKv.REJECT_RECOGNIZE_INS_RECEIVED
                )
                AiSpeechEngine.INSTANCE.onDialogReject(dialogId)
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
            }

            is Dialog.ExitContinuousDialog -> {
                // 规避二次唤醒打断上次连续对话后，下发的上轮退出连续对话指令.
                val requestId = AiSpeechEngine.INSTANCE.requestId()
                if (dialogId != requestId) {
                    Timber.tag(TAG).d("it not $requestId ExitContinuousDialog cmd.")
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
                    return
                }

                if (!EngineStateMachine.isIdle() && !isAlipayStatus()) {
                    AiSpeechEngine.INSTANCE.finishSession(
                        byCloud = true,
                        reason = "收到云端退出:ExitContinuousDialog.",
                        isNeedAudioResume = true
                    )
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
                } else {
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
                }
            }

            is Dialog.ExitMultipleTurn -> {
                // AiSpeechEngine.INSTANCE.postEvent(General.ClearSession())
                if (isAlipayStatus()) {
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
                    Timber.tag(TAG).i("ExitMultipleTurn isAlipayStatus ignore")
                    return
                }
                AiSpeechEngine.INSTANCE.sendEventToDevice(event = DeviceEvent.VOICE_STOP_CAPTURE)
                TtsDependency.processDependencyTask(instruction.header.dialogId.getOrNull()) {
                    AiSpeechEngine.INSTANCE.finishSession(
                        byCloud = true,
                        reason = "收到云端:Dialog.ExitMultipleTurn",
                        isNeedAudioResume = true
                    )
                }
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
            }

            is Dialog.IllegalContent -> {
                val code = payload.code.getOrNull()
                if ((code == 411 && payload.isDeleteContent.getOrNull() == true) || code == 40700001) {
                    dialogIllegalStatusChange(true, dialogId)
                    payload.msg?.let {
                        Timber.tag(TAG).i("IllegalContent stopTts")
                        AiSpeechEngine.INSTANCE.stopTts(
                            dialogId = dialogId,
                            stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                                calledFrom = "IllegalContent",
                                needResumeMediaPlayer = null,
                                needStopMediaPlayer = null,
                                stopReason = null
                            )
                        )
                        AiSpeechEngine.INSTANCE.startTts(it)
                    }
                }
            }
        }
    }

    private fun onDialogIllegal(instruction: Instruction<*>) {
        Timber.tag(TAG).i("onDialogIllegal $dialogIsIllegal")
        if (dialogIsIllegal) {
            AiSpeechEngine.INSTANCE.onDialogIllegal(
                instruction.header.transactionId.getOrNull(),
                instruction.header.dialogId.getOrNull()
            )
        }
    }

    private fun processExecution(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).i("processExecution:${instruction?.toString()}")
        when (instruction?.payload) {
            is Execution.CrossDeviceControl,
            is Execution.CrossDeviceControlPhone -> {
                processPhoneCmd(header, dialogId, instruction)
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
            }

            else -> {
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
            }
        }
    }

    private fun processMultiModal(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processMultiModal:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is MultiModal.UploadMultiModal -> {
                dialogId?.let {
                    AiSpeechEngine.INSTANCE.onUploadMultiModalEvent(
                        header?.transactionId?.getOrNull(),
                        dialogId
                    )
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
                } ?: recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
            }

            is MultiModal.FileUploadResult -> {
                payload.fileUploadInfoList.firstOrNull()?.let {
                    val status = it.uploadStatus
                    if (status == MultiModal.FileUploadStatus.UPLOAD_SUCCESS) {
                        AiSpeechEngine.INSTANCE.onImageFileId(dialogId, it.fileId)
                        recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
                    } else {
                        recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
                    }
                    if (status != MultiModal.FileUploadStatus.UPLOADING &&
                        status != MultiModal.FileUploadStatus.UNKNOWN
                    ) {
                        dialogId?.let {
                            uploadImageDialogId.add(dialogId)
                            when (status) {
                                MultiModal.FileUploadStatus.UPLOAD_SUCCESS -> {
                                    EventTrack.trackImageQAEvent(
                                        uploadRequestId = uploadImageDialogId.first(),
                                        uploadEventId = uploadImageDialogId.last(),
                                        uploadResult = "UPLOAD_SUCCESS"
                                    )
                                }

                                MultiModal.FileUploadStatus.UPLOAD_FAILED -> {
                                    EventTrack.trackImageQAEvent(
                                        uploadRequestId = uploadImageDialogId.first(),
                                        uploadEventId = uploadImageDialogId.last(),
                                        uploadResult = "UPLOAD_FAILED"
                                    )
                                }

                                else -> Unit
                            }
                            uploadImageDialogId.clear()
                        }
                    }
                } ?: recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
            }

            else -> recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
        }
    }

    private fun processSpeechRecognizer(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).i("processSpeechRecognizer:${instruction?.toString()}")
        EventTrack.onEventTrackTime(key = EventTrackKv.ASR_RECOGNIZE_SENT)
        when (val payload = instruction?.payload) {
            is SpeechRecognizer.RecognizeResult -> {
                EventTrack.onEventTrack(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_REQUEST_ID,
                    value = dialogId ?: ""
                )
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.ASR_FIRST_PARTIAL
                )
                val isFinal = payload.isFinal
                val text = payload.results.firstOrNull()?.text
                text?.let {
                    if (text.isNotNullOrEmpty()) {
                        AiSpeechEngine.INSTANCE.closePingPong("RecognizeResult")
                    }
                    if (it != curAsr) {
                        EventTrack.onEventTrackTime(
                            dialogId = dialogId,
                            key = EventTrackKv.ASR_FIRST_SAME_FINAL,
                            forceUpdate = true
                        )
                    }
                    curAsr = it
                    EventTrack.onEventTrackTime(
                        dialogId = dialogId,
                        key = EventTrackKv.ASR_FIRST_TEXT
                    )
                }
                if (isFinal) {
                    EventTrack.onEventTrackTime(
                        dialogId = dialogId,
                        key = EventTrackKv.ASR_FINAL
                    )
                    EventTrack.onEventTrack(
                        dialogId = dialogId,
                        key = EventTrackKv.STATE_ASR_FINAL_SIZE,
                        value = text?.length ?: 0
                    )
                    // fix GTKGLASS-13271
                    AiSpeechEngine.INSTANCE.onQueryRecognize(
                        header?.transactionId?.getOrNull(),
                        dialogId,
                        text,
                        isFinal = false,
                        isFromPostImageForLinkImgId = true,
                        instructionJson = null
                    )
                }
                AiSpeechEngine.INSTANCE.onAsrInput(
                    dialogId = dialogId,
                    input = text,
                    isFinal = isFinal
                )
                Timber.tag(TAG).d("ASR识别:$text; 是否识别完成:$isFinal")
            }
        }
    }

    private fun processSpeechSynthesizer(
        header: InstructionHeader,
        dialogId: String?,
        instruction: Instruction<*>?,
        isStream: Boolean
    ) {
        Timber.tag(TAG).d("processSpeechSynthesizer:$isStream,${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is SpeechSynthesizer.Speak -> {
                TtsDependency.onReceiveTtsSpeech(dialogId)
                if (isStream) {
                    EventTrack.onEventTrackTime(
                        dialogId = dialogId,
                        key = EventTrackKv.NLP_SPEAK_URL_INS_RECEIVED
                    )
                    payload.url?.getOrNull()?.let { url ->
                        callback.onReceiveTtsUrl(
                            dialogId = dialogId,
                            urls = listOf(url),
                            audioType = SpeechEngineProxyImpl.Companion.AudioType.TTS
                        )
                        recordResult(dialogId, ProcessResult.Success)
                    } ?: recordResult(dialogId, ProcessResult.Filtered)
                } else {
                    val ttsStr = payload.text
                    Timber.tag(TAG).d("TTS播报内容:$dialogId,$ttsStr")
                    AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                        header.transactionId.getOrNull(),
                        dialogId,
                        ttsStr,
                        true,
                        instructionJson = instruction.toString()
                    )
                    recordResult(dialogId, ProcessResult.Success)
                }
            }

            is SpeechSynthesizer.SpeakStream -> {
                EventTrack.onEventTrackTime(
                    dialogId = dialogId,
                    key = EventTrackKv.NLP_SPEAK_STREAM_INS_RECEIVED
                )
                if (isStream) {
                    val ttsStr = payload.text
                    streamStr.append(ttsStr)
                    AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                        header.transactionId?.getOrNull(),
                        dialogId,
                        streamStr.toString(),
                        false,
                        instructionJson = instruction.toString()
                    )
                } else {
                    val ttsStr = payload.text
                    Timber.tag(TAG).d("TTS播报内容:$dialogId,$ttsStr")
                    AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                        header.transactionId?.getOrNull(),
                        dialogId,
                        ttsStr,
                        true,
                        instructionJson = instruction.toString()
                    )
                }
                recordResult(dialogId, ProcessResult.Success)
            }

            else -> recordResult(dialogId, ProcessResult.Filtered)
        }
    }

    @Suppress("LongMethod")
    private fun processAudioPlayer(
        header: InstructionHeader,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processAudioPlayer:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is AudioPlayer.Play -> {
                payload.audioItems?.let { items ->
                    val urls = mutableListOf<String>()
                    items.onEach { item ->
                        if (item.stream.url.isNotEmpty()) {
                            urls.add(item.stream.url)
                        }
                    }
                    callback.onReceiveTtsUrl(
                        dialogId = dialogId,
                        urls = urls,
                        audioType = SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO
                    )
                    recordResult(dialogId, ProcessResult.Success)
                } ?: recordResult(dialogId, ProcessResult.Filtered)
            }

            is AudioPlayer.PlayApp -> {
                payload.audioItems?.let { items ->
                    playItems(
                        dialogId,
                        header.fullName,
                        payload.app.pkgName,
                        items
                    )
                    recordResult(dialogId, ProcessResult.Success)
                } ?: recordResult(dialogId, ProcessResult.Filtered)
            }

            is AudioPlayer.PlayFavorites -> {
                MusicApiService.INSTANCE.playFavouriteList(
                    cp = payload.appName.getOrNull(),
                    0,
                    payload.type.name
                ) { success, code, message ->
                    Timber.tag(TAG).d("playFavouriteList:$success")
                    if (!success) {
                        EventTrack.onEventError(
                            dialogId = dialogId,
                            key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                            type = header.fullName,
                            errorCode = code,
                            errorMsg = message
                        )
                        recordResult(dialogId, ProcessResult.Failed)
                    } else {
                        AiSpeechEngine.INSTANCE.saveCurrentMediaType(SpeechEngineProxyImpl.Companion.AudioType.MUSIC)
                        recordResult(dialogId, ProcessResult.Success)
                    }
                }
            }

            is AudioPlayer.AddToFavorites -> {
                MusicApiService.INSTANCE.curSong(true) { song ->
                    song?.let {
                        MusicApiService.INSTANCE.addToFavourite(
                            cp = payload.appName.getOrNull(),
                            mid = song.mid
                        ) { success, code, message ->
                            Timber.tag(TAG).d("AddToFavorites:$success")
                            if (!success) {
                                EventTrack.onEventError(
                                    dialogId = dialogId,
                                    key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                                    type = header.fullName,
                                    errorCode = code,
                                    errorMsg = message
                                )
                                recordResult(dialogId, ProcessResult.Failed)
                            } else {
                                recordResult(dialogId, ProcessResult.Success)
                            }
                        }
                    } ?: run {
                        recordResult(dialogId, ProcessResult.Failed)
                        Timber.tag(TAG).e("AddToFavorites failed by song id is null")
                    }
                }
            }

            is AudioPlayer.CancelFromFavorites -> {
                MusicApiService.INSTANCE.curSong(true) { song ->
                    song?.let {
                        MusicApiService.INSTANCE.removeFromFavourite(
                            cp = payload.appName.getOrNull(),
                            mid = song.mid
                        ) { success, code, message ->
                            Timber.tag(TAG).d("CancelFromFavorites:$success")
                            if (!success) {
                                EventTrack.onEventError(
                                    dialogId = dialogId,
                                    key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                                    type = header.fullName,
                                    errorCode = code,
                                    errorMsg = message
                                )
                                recordResult(dialogId, ProcessResult.Failed)
                            } else {
                                recordResult(dialogId, ProcessResult.Success)
                            }
                        }
                    } ?: run {
                        recordResult(dialogId, ProcessResult.Failed)
                        Timber.tag(TAG).e("CancelFromFavorites failed by song id is null")
                    }
                }
            }

            else -> recordResult(dialogId, ProcessResult.Filtered)
        }
    }

    @Suppress("LongMethod")
    private fun processPlaybackController(
        header: InstructionHeader,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processPlaybackController:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is PlaybackController.Play -> {
//                MusicApiService.INSTANCE.play()
                AiSpeechEngine.INSTANCE.action(header.fullName, payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            is PlaybackController.ContinuePlaying -> {
                AiSpeechEngine.INSTANCE.action(header.fullName, payload)
                val currentMediaType = AiSpeechEngine.INSTANCE.getCurrentMediaType()
                when {
                    currentMediaType == SpeechEngineProxyImpl.Companion.AudioType.MUSIC ||
                        payload.scene?.getOrNull() == PlaybackController.SceneType.MUSIC -> {
                        Timber.tag(TAG).d("ContinuePlaying MUSIC")
                        MusicApiService.INSTANCE.resume()
                        AiSpeechEngine.INSTANCE
                            .saveCurrentMediaType(SpeechEngineProxyImpl.Companion.AudioType.MUSIC)
                    }

                    currentMediaType == SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO &&
                        AiSpeechEngine.INSTANCE.isLongAudioPausing() == true -> {
                        Timber.tag(TAG).d("ContinuePlaying LONG_AUDIO")
                        AiSpeechEngine.INSTANCE.resumeMediaPlayer("ContinuePlaying LONG_AUDIO")
                        if (MusicApiService.INSTANCE.isPlaying()) {
                            MusicApiService.INSTANCE.stop()
                        }
                    }
                }
                recordResult(dialogId, ProcessResult.Success)
            }

            is PlaybackController.Pause -> {
//                MusicApiService.INSTANCE.pause()
                AiSpeechEngine.INSTANCE.action(header.fullName, payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            is PlaybackController.Stop -> {
                MusicApiService.INSTANCE.stop()
                AiSpeechEngine.INSTANCE.action(header.fullName, payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            is PlaybackController.Next -> {
//                MusicApiService.INSTANCE.playNext()
                AiSpeechEngine.INSTANCE.action(header.fullName, payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            is PlaybackController.Prev -> {
//                MusicApiService.INSTANCE.playPre()
                AiSpeechEngine.INSTANCE.action(header.fullName, payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            is PlaybackController.Seek -> {
                if (MusicApiService.INSTANCE.isPlaying() || MusicApiService.INSTANCE.isPause()) {
                    val seekTime: Long = when (payload.reference) {
                        PlaybackController.ReferenceDef.START -> {
                            recordResult(dialogId, ProcessResult.Success)
                            val curTime = MusicApiService.INSTANCE.curTime()
                            payload.deltaInMs - curTime
                        }

                        PlaybackController.ReferenceDef.CURRENT -> {
                            recordResult(dialogId, ProcessResult.Success)
                            payload.deltaInMs.toLong()
                        }

                        PlaybackController.ReferenceDef.END -> {
                            recordResult(dialogId, ProcessResult.Success)
                            val curTime = MusicApiService.INSTANCE.curTime()
                            val total = MusicApiService.INSTANCE.totalTime()
                            total - payload.deltaInMs - curTime
                        }

                        else -> {
                            recordResult(dialogId, ProcessResult.Success)
                            0L
                        }
                    }
                    if (seekTime > 0L) {
                        seekForward(seekTime, dialogId, header)
                    } else {
                        seekBack(seekTime, dialogId, header)
                    }
                } else {
                    Timber.tag(TAG).d("not target status")
                    recordResult(dialogId, ProcessResult.Failed)
                    AiSpeechEngine.INSTANCE.startTts("没有可操作的资源")
                }
            }

            is PlaybackController.StartOver -> {
                if (MusicApiService.INSTANCE.isPlaying() || MusicApiService.INSTANCE.isPause()) {
                    val curTime = MusicApiService.INSTANCE.curTime()
                    MusicApiService.INSTANCE.seekBack(offsetMs = curTime) { success, code, message ->
                        Timber.tag(TAG).d("seekBack $success")
                        if (!success) {
                            EventTrack.onEventError(
                                dialogId = dialogId,
                                key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                                type = header.fullName,
                                errorCode = code,
                                errorMsg = message
                            )
                            AiSpeechEngine.INSTANCE.startTts(OPERATION_FAILED_TIPS)
                            recordResult(dialogId, ProcessResult.Failed)
                        } else {
                            if (MusicApiService.INSTANCE.isPause()) {
                                MusicApiService.INSTANCE.resume()
                            }
                            recordResult(dialogId, ProcessResult.Success)
                        }
                    }
                } else {
                    Timber.tag(TAG).d("not target App")
                    AiSpeechEngine.INSTANCE.startTts(OPERATION_FAILED_TIPS)
                    recordResult(dialogId, ProcessResult.Failed)
                }
            }

            is PlaybackController.SetProperty -> {
                if (payload.name == "LOOP_MODE" && MusicApiService.INSTANCE.isPlaying()) {
                    MusicApiService.INSTANCE.setPlayMode(playMode = payload.value) { success, code, message ->
                        if (!success) {
                            EventTrack.onEventError(
                                dialogId = dialogId,
                                key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                                type = header.fullName,
                                errorCode = code,
                                errorMsg = message
                            )
                            recordResult(dialogId, ProcessResult.Failed)
                        } else {
                            recordResult(dialogId, ProcessResult.Success)
                        }
                    }
                }
            }

            else -> recordResult(dialogId, ProcessResult.Filtered)
        }
    }

    private fun seekBack(
        seekTime: Long,
        dialogId: String?,
        header: InstructionHeader
    ) {
        MusicApiService.INSTANCE.seekBack(offsetMs = abs(seekTime)) { success, code, message ->
            if (!success) {
                EventTrack.onEventError(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                    type = header.fullName,
                    errorCode = code,
                    errorMsg = message
                )
                AiSpeechEngine.INSTANCE.startTts(OPERATION_FAILED_TIPS)
                recordResult(dialogId, ProcessResult.Failed)
            } else {
                if (MusicApiService.INSTANCE.isPause()) {
                    MusicApiService.INSTANCE.resume()
                }
                recordResult(dialogId, ProcessResult.Success)
            }
        }
    }

    private fun seekForward(
        seekTime: Long,
        dialogId: String?,
        header: InstructionHeader
    ) {
        MusicApiService.INSTANCE.seekForward(offsetMs = seekTime) { success, code, message ->
            if (!success) {
                EventTrack.onEventError(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                    type = header.fullName,
                    errorCode = code,
                    errorMsg = message
                )
                AiSpeechEngine.INSTANCE.startTts(OPERATION_FAILED_TIPS)
                recordResult(dialogId, ProcessResult.Failed)
            } else {
                if (MusicApiService.INSTANCE.isPause()) {
                    MusicApiService.INSTANCE.resume()
                }
                recordResult(dialogId, ProcessResult.Success)
            }
        }
    }

    private fun processWearableController(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processWearableController:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is WearableController.Execute -> {
                when (payload.directive) {
                    WearableController.DirectiveType.TAKE_A_PICTURE -> {
                        AiSpeechEngine.INSTANCE.execute("take_a_picture")
                        recordResult(dialogId, ProcessResult.Success)
                    }

                    WearableController.DirectiveType.START_VIDEO -> {
                        AiSpeechEngine.INSTANCE.execute("start_video")
                        recordResult(dialogId, ProcessResult.Success)
                    }

                    WearableController.DirectiveType.END_VIDEO -> {
                        AiSpeechEngine.INSTANCE.execute("end_video")
                        recordResult(dialogId, ProcessResult.Success)
                    }

                    WearableController.DirectiveType.START_RECORDING -> {
                        if (isAlipayStatus()) {
                            Timber.tag(TAG).d("alipay is paying cant not open recorder")
                            AiSpeechEngine.INSTANCE.startTts("正在扫码支付中 无法开启录音")
                            return
                        }
                        AiSpeechEngine.INSTANCE.execute("start_recording")
                    }

                    WearableController.DirectiveType.END_RECORDING -> {
                        AiSpeechEngine.INSTANCE.stopRecord()
                    }

                    WearableController.DirectiveType.ENTER_REAL_TIME_TRANSLATION -> {
                        // 根据GTKGLASS-12461，后续没有语控了，需要用户从app首页点击进入
                        // AiSpeechEngine.INSTANCE.startRecordTranslate()
                    }
                    // 面对面翻译取消 --张培钰
                    WearableController.DirectiveType.ENTER_FACE_TO_FACE_TRANSLATION -> {
                        AiSpeechEngine.INSTANCE.execute("end_video")
                    }

                    else -> {
                        recordResult(dialogId, ProcessResult.Filtered)
                    }
                }
            }

            is WearableController.SetProperty -> {
                val property = payload.properties.firstOrNull()
                if (null != property) {
                    val propertyName = property.name
                    val value = property.value.getOrNull()
                    if (null != value) {
                        AiSpeechEngine.INSTANCE.setProperty(propertyName.name, value.toJson())
                        recordResult(dialogId, ProcessResult.Success)
                    } else {
                        recordResult(dialogId, ProcessResult.Filtered)
                    }
                } else {
                    recordResult(dialogId, ProcessResult.Filtered)
                }
            }

            else -> {
                recordResult(dialogId, ProcessResult.Filtered)
            }
        }
    }

    private fun processSys(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processSys:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is Sys.Exception -> {
                EventTrack.onEventIncrease(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_NLP_EXCEPTIONS
                )
                EventTrack.onEventTrack(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_NLP_EXCEPTION_PAYLOAD,
                    value = payload.toJson()
                )
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
            }

            is Sys.Abort -> {
                streamInterrupt = TextUtils.equals(payload.reason, "stream_interrupted")
                EventTrack.onEventIncrease(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_NLP_SYSTEM_ABORT
                )
                recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
                // System.Abort 指令作为有效的NLP指令处理.
                AiSpeechEngine.INSTANCE.onNlpEnter(
                    dialogId,
                    header?.fullName,
                    false,
                    reCountDown = false
                )
                if (TextUtils.equals(payload.reason, "judge_cancel")) {
                    AiSpeechEngine.INSTANCE.finishSession(
                        byCloud = true,
                        reason = payload.reason,
                        isNeedAudioResume = AiSpeechEngine.INSTANCE.isLongAudioPlaying()
                    )
                }
            }

            is Sys.SetProperty -> {
                // 静音指令 Sys.SetProperty{name ="MUTE_MODE",  value="ON"}
                if (payload.name == "MUTE_MODE") {
                    AiSpeechEngine.INSTANCE.action(header?.fullName ?: "", payload)
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Success)
                } else {
                    recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
                }
            }

            is Sys.Pong -> {
                Timber.tag(TAG).d("接收Pong的消息 $dialogId")
                dialogId?.let {
                    AiSpeechEngine.INSTANCE.pongNum(dialogId)
                }
            }

            else -> recordResult(header?.dialogId?.getOrNull(), ProcessResult.Filtered)
        }
    }

    @Suppress("LongMethod")
    private fun processTemplate(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processTemplate:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is Template.Toast -> {
                val ttsStr = payload.text
                Timber.tag(TAG).d("TTS播报内容:$dialogId,$ttsStr")
                AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                    header?.transactionId?.getOrNull(),
                    dialogId,
                    ttsStr,
                    true,
                    instructionJson = instruction.toString()
                )
                recordResult(dialogId, ProcessResult.Success)
            }
            is Template.PlayInfo -> {
                val ttsStr = payload.items.takeWhile { it.title.mainTitle.isNotEmpty() }
                    .joinToString("") { it.title.mainTitle }
                Timber.tag(TAG).d("TTS播报媒体内容:$dialogId,$ttsStr")
                if (ttsStr.isNotEmpty()) {
                    AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                        header?.transactionId?.getOrNull(),
                        dialogId,
                        ttsStr,
                        true,
                        instructionJson = instruction.toString()
                    )
                    recordResult(dialogId, ProcessResult.Success)
                } else {
                    recordResult(dialogId, ProcessResult.Filtered)
                }
            }

            is Template.LLMRichContent -> {
                val title = payload.title
                val description = payload.description
                val iconUrl = payload.icon.getOrNull()?.sources?.firstOrNull()?.url ?: ""
                streamCardConfig = Triple(title, description, iconUrl)
                lLMRichContentCache = instruction
                recordResult(dialogId, ProcessResult.Filtered)
            }

            is Template.FeedbackCard -> {
                Timber.tag(TAG).d("Template.FeedbackCard %s", dialogId)
                TtsDependency.processDependencyTask(dialogId) {
                    // 处理语音反馈
                    AiSpeechEngine.INSTANCE.postFeedBackEvent()
                    AiSpeechEngine.INSTANCE.enterVoiceFeedBack("FeedbackCard")
                }
                recordResult(dialogId, ProcessResult.Success)
            }
            is Template.Query -> {
                queryAsr = payload?.text
                AiSpeechEngine.INSTANCE.onQueryRecognize(
                    header?.transactionId?.getOrNull(),
                    dialogId,
                    payload?.text,
                    true,
                    instructionJson = instruction.toString()
                )
                recordResult(dialogId, ProcessResult.Success)
            }

            is Template.ToastStream -> {
                val markdownText = (instruction.payload as Template.ToastStream).markdownText
                displayText.append(markdownText)
                dialogId?.let {
                    toastStreamCache = instruction
                }
                recordResult(dialogId, ProcessResult.Success)
            }

            is Template.ImageQAContent -> {
                AiSpeechEngine.INSTANCE.onImageQAContent(
                    dialogId,
                    header?.transactionId?.getOrNull(),
                    instruction
                )
                recordResult(dialogId, ProcessResult.Success)
            }

            // 记忆停车位卡片指令
            is Template.ParkingCard -> {
                val notification = payload.notification.getOrNull()

                if (notification != null) {
                    val title = notification.title.get()
                    val subTitle = notification.subTitle.get()
                    val url = notification.jump.get().url.get()
                    Timber.tag(TAG).d("notification: $notification,title: $title, subTitle: $subTitle, url: $url")
                    AiSpeechEngine.INSTANCE.onParkingCard(
                        dialogId = dialogId,
                        sessionId = header?.transactionId?.getOrNull(),
                        title = title,
                        subTitle = subTitle,
                        url = url,
                        instructionJson = instruction.toString()
                    )
                }
            }
            else -> recordResult(dialogId, ProcessResult.Filtered)
        }
    }

    private fun processSpeaker(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processSpeaker:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is Speaker.AdjustVolume -> {
                AiSpeechEngine.INSTANCE.action(header?.fullName ?: "", payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            is Speaker.SetVolume -> {
                AiSpeechEngine.INSTANCE.action(header?.fullName ?: "", payload)
                recordResult(dialogId, ProcessResult.Success)
            }

            else -> recordResult(dialogId, ProcessResult.Filtered)
        }
    }

    private fun processGeneral(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processGeneral:${instruction?.toString()}")
        uploadImageDialogId.clear()
        when (val payload = instruction?.payload) {
            is General.Preprocess -> {
                // 预判指令,收到通知眼镜拍照传图到手机
                val instructions = APIUtils.readInstructions(payload.instructions)
                instructions.onEach {
                    if (AIApiConstants.MultiModal.UploadMultiModal.equals(it?.fullName, true)) {
                        AiSpeechEngine.INSTANCE.sendEventToDevice(
                            it?.header?.transactionId?.getOrNull(),
                            it?.header?.dialogId?.getOrNull(),
                            DeviceEvent.IMAGE_CAPTURE_AND_TRANS
                        )
                        recordResult(dialogId, ProcessResult.Success)
                    } else {
                        recordResult(dialogId, ProcessResult.Filtered)
                    }
                }
            }

            is General.Push -> {
                // 收到后有缓存图片直接上传缓存图片，没有则通知眼镜拍照+传图上云
                val instructions = APIUtils.readInstructions(payload.instructions)
                instructions.onEach {
                    Timber.tag(TAG).d("General.Push:$it")
                    AiSpeechEngine.INSTANCE.onNlpEnter(dialogId, header?.fullName, false)
                    if (AIApiConstants.Application.UploadResource.equals(it?.fullName, true)) {
                        // 获取 UploadResource 对象
                        val uploadResource = it.payload as? Application.UploadResource
                        Timber.tag(TAG).d("General.Push:uploadResource $it")
                        val jsonObject = JSONObject()
                        uploadResource?.imageParam?.let { imageParam ->
                            jsonObject.put(
                                "type",
                                imageParam.getOrNull()?.circleImageType?.getOrNull()?.name
                            )
                        }
                        AiSpeechEngine.INSTANCE.onUploadMultiModalEvent(
                            it?.header?.transactionId?.getOrNull(),
                            it?.header?.dialogId?.getOrNull() ?: "",
                            payload = jsonObject.toString(),
                            isPush = true
                        )
                        uploadImageDialogId.add(it?.header?.dialogId?.getOrNull() ?: "")
                        recordResult(dialogId, ProcessResult.Success)
                    } else {
                        recordResult(dialogId, ProcessResult.Filtered)
                    }
                }
            }

            else -> {
                recordResult(dialogId, ProcessResult.Filtered)
            }
        }
    }

    private fun processOther(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processOther:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            // 云端下发CheckApps时,有下发TTS,不做处理.
//            is Application.CheckApps -> {
//                payload.apps.firstOrNull()?.let { appItem ->
//                    MusicApiService.INSTANCE.onError(appItem.pkgName, ErrorCode.NOT_INSTALL)
//                }
//            }
            // 用户主动退出.
            is UIController.Navigate -> {
                if (payload.operation == UIController.NavigateOp.EXIT && !isAlipayStatus()) {
                    AiSpeechEngine.INSTANCE.finishSession(
                        byCloud = true,
                        reason = "收到云端退出:UIController.NavigateOp.EXIT.",
                        isNeedAudioResume = AiSpeechEngine.INSTANCE.isLongAudioPlaying()
                    )
                    recordResult(dialogId, ProcessResult.Success)

                    val exitInstruction = dialogId?.let {
                        AiSpeechEngine.INSTANCE.generateSysExceptionInstruction(
                            InstructionConst.SysExceptionCode.EXIT_WITH_NO_TOAST,
                            context.getString(R.string.tip_exit_with_no_toast),
                            it
                        )
                    }

                    AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                        header?.transactionId?.getOrNull(),
                        dialogId,
                        context.getString(R.string.tip_exit_with_no_toast),
                        true,
                        instructionJson = exitInstruction.toString()
                    )

                    EventTrack.onEventTrack(
                        dialogId = dialogId,
                        key = EventTrackKv.STATE_CANCEL_MSG,
                        value = CANCEL_MSG_CANCEL_BY_CLOUD
                    )
                    endTracking(dialogId)
                } else {
                    recordResult(dialogId, ProcessResult.Filtered)
                }
            }

            is FullScreenTemplate.StreamDialog -> {
                payload.avatar.getOrNull()?.let {
                    val title = it.name.getOrNull() ?: ""
                    val avatar = it.icon.getOrNull()?.url ?: ""
                    streamPageConfig = Triple(title, avatar, payload.welcome.getOrNull())
                }
                when (val dialogType = payload.type.getOrNull()) {
                    StreamDialogType.VIDEO_SKILL,
                    StreamDialogType.ROLE_PLAY_SKILL,
                    StreamDialogType.AGENT_SKILL -> {
                        AiSpeechEngine.INSTANCE.enterStandby(dialogType.name)
                        AiSpeechEngine.INSTANCE.onStreamDialogEnter(
                            sessionId = instruction.header.transactionId.getOrNull(),
                            dialogId = instruction.header.dialogId.getOrNull(),
                            streamType = convertToStreamType(dialogType),
                            streamId = dialogType.name,
                            cardConfig = streamCardConfig,
                            pageConfig = streamPageConfig,
                            instructionJson = lLMRichContentCache.toString()
                        )
                        recordResult(dialogId, ProcessResult.Success)
                    }

                    else -> {
                        recordResult(dialogId, ProcessResult.Filtered)
                    }
                }
            }

            else -> {
                recordResult(dialogId, ProcessResult.Filtered)
                Timber.d("忽略处理:${header?.fullName}")
            }
        }
    }

    // 云端判停发送首轮语音数据
    private fun processOffline(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processOffline:${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is CloudStop -> {
                val stopAudioDuration = payload.stopAudioDuration.getOrNull()
                val currentRoundUsedAudioDuration =
                    payload.currentRoundUsedAudioDuration.getOrNull()
                if (null != stopAudioDuration && null != currentRoundUsedAudioDuration) {
                    val startTime = stopAudioDuration - currentRoundUsedAudioDuration
                    AiSpeechEngine.INSTANCE.offlineCloudStop(startTime.toLong())
                }
            }
        }
    }

    // 收到支付意图通知扫码
    @SuppressLint("ImplicitSamInstance")
    private fun processAlipay(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processAlipay:$queryAsr,${instruction?.toString()}")
        when (val payload = instruction?.payload) {
            is Application.ScanPay -> {
                dialogId?.let {
                    AiSpeechEngine.INSTANCE.restartTimer(
                        "ScanPay",
                        AiSpeechEngine.INSTANCE.countdownTime()
                    )
                    AiSpeechEngine.INSTANCE.stopTts(
                        stopOptions = TtsStopOptions(
                            calledFrom = "processAlipay",
                            needResumeMediaPlayer = AiSpeechEngine.INSTANCE.isLongAudioPlaying(),
                            needStopMediaPlayer = null,
                            stopReason = null
                        )
                    )
                    if (AiSpeechEngine.INSTANCE.isAlipayDemoMode()) {
                        AiSpeechEngine.INSTANCE.scanPayWithDemoMode(
                            instruction.header.transactionId.getOrNull(),
                            it,
                            queryAsr ?: ""
                        )
                    } else {
                        AiSpeechEngine.INSTANCE.scanPay(
                            instruction.header.transactionId.getOrNull(),
                            it,
                            queryAsr ?: ""
                        )
                    }
                    recordResult(dialogId, ProcessResult.Success)
                }
            }
        }
    }

    private fun processPhoneCmd(
        header: InstructionHeader?,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        Timber.tag(TAG).d("processPhoneCmd:${header?.name}")
        phoneBridge.sendInstruction(context, dialogId, instruction)
    }

    private fun playItems(
        dialogId: String?,
        fullName: String,
        cp: String,
        items: List<AudioPlayer.AppAudioItem>
    ) {
        val ids = ArrayList<String>(items.size)
        items.forEach { item ->
            ids.add(item.audioId)
        }
        MusicApiService.INSTANCE.playListAtIndex(cp = cp, ids = ids) { success, code, message ->
            Timber.tag(TAG).d("ids[0]: ${ids.firstOrNull()} playItems:$success,$code,$message")
            AiSpeechEngine.INSTANCE
                .saveCurrentMediaType(SpeechEngineProxyImpl.Companion.AudioType.MUSIC)
            if (!success) {
                EventTrack.onEventError(
                    dialogId = dialogId,
                    key = EventTrackKv.STATE_NLP_EXEC_FAILED,
                    type = fullName,
                    errorCode = code,
                    errorMsg = message
                )
            }
        }
    }

    private fun convertToStreamType(type: StreamDialogType): String {
        return when (type) {
            StreamDialogType.ROLE_PLAY_SKILL -> StreamType.ROLE_PLAY
            StreamDialogType.AGENT_SKILL -> StreamType.AGENT
            else -> StreamType.STANDBY
        }
    }

    private fun isAlipayStatus(): Boolean {
        val isAlipayStatus =
            AiSpeechEngine.INSTANCE.getAlipayStatus() || AiSpeechEngine.INSTANCE.getAlipayExitStatus()
        Timber.tag(TAG).d("isAlipayStatus:$isAlipayStatus")
        return isAlipayStatus
    }

    companion object {
        private const val TAG = "InstructionProcessor"
        private const val MIN_NLP_CMD_COUNT = 2

        private const val OP_TRACE_RESULT_CANCEL = "cancel"
        private const val OP_TRACE_RESULT_SUCCESS = "success"
        private const val OP_TRACE_RESULT_FAIL = "fail"

        private const val CANCEL_MSG_RESTART = "startTracking restart"
        private const val CANCEL_MSG_CANCEL_BY_CLOUD = "Cancel by cloud."
        private const val OPERATION_FAILED_TIPS = "本次操作失败了，请手动操作一下吧"
    }
}

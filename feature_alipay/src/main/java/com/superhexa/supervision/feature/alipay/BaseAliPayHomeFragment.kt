package com.superhexa.supervision.feature.alipay

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.core.os.bundleOf
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.alipay.model.AliPayGuideViewModel
import com.superhexa.supervision.feature.alipay.router.HexaRouter
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.AlertStatus.ON_SITE_RECORDING
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.AlertStatus.RECORD_VIDEO
import com.superhexa.supervision.feature.xiaoai.service.MiLiteHelper
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.ALIPAY_HOME_FRAGMENT
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FRAGMENT_FIRM_TAG
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

@Route(path = ALIPAY_HOME_FRAGMENT)
class BaseAliPayHomeFragment : BaseComposeFragment() {
    private val viewModel: AliPayGuideViewModel by lazy {
        ViewModelProvider(
            this,
            ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
        )[AliPayGuideViewModel::class.java]
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.addRecordObserver(this)
    }

    override fun onResume() {
        super.onResume()
        viewModel.checkBindStatus()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.removeRecordStateObserver()
    }

    override val contentView: @Composable () -> Unit = {
        val isAliPayBind by viewModel.isAliPayBind.collectAsState()

        ConstraintLayout(modifier = Modifier.fillMaxSize()) {
            val (title, tip, action) = createRefs()
            com.superhexa.supervision.feature.alipay.component.CommonAliPayTitleBar(
                null,
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top)
                    bottom.linkTo(tip.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true,
                rightIcRes = if (isAliPayBind) R.drawable.ic_o95_alipay_settings else 0,
                rightIcClick = {
                    HexaRouter.Alipay.navigateToAliPaySettings(this@BaseAliPayHomeFragment)
                },
                onExceedClickLimit = {
                    HexaRouter.Alipay.navigateToAlipayTest(this@BaseAliPayHomeFragment)
                }
            ) { navigator.pop() }

            com.superhexa.supervision.feature.alipay.component.AliPayList(
                modifier = Modifier.constrainAs(tip) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(title.bottom, margin = Dp_12)
                    bottom.linkTo(action.top)
                    height = Dimension.fillToConstraints
                }
            )
            if (!isAliPayBind) {
                BottomAction(
                    modifier = Modifier.constrainAs(action) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        top.linkTo(tip.bottom, margin = Dp_12)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                )
            }
        }
    }

    @Composable
    private fun BottomAction(
        modifier: Modifier
    ) {
        val brush = Brush.horizontalGradient(listOf(Color26EAD9, Color17CBFF))
        com.superhexa.supervision.feature.alipay.component.AliPayGradientButton(
            modifier = modifier
                .fillMaxWidth()
                .height(Dp_50)
                .padding(horizontal = Dp_30),
            brush,
            ColorBlack,
            textRes = R.string.text_alipay_binding,
            onConfirm = {
                checkStatus {
                    val dialog = AliPayBindDialog()
                    dialog.setOnConfirmAction {
                        Timber.d("confirmAction")
                        checkStatus { AlipaySDKManager.INSTANCE.startBinding() }
                    }
                    activity?.let {
                        dialog.show(
                            it.supportFragmentManager,
                            "AliPayBindDialog"
                        )
                    }
                }
            }
        )
    }

    private fun checkStatus(function: () -> Unit?) {
        lifecycleScope.launch(Dispatchers.IO) {
            val status = MiLiteHelper.getRecorderStatus()
            val isTranslate = MiLiteHelper.getIsTranslating()
            val isNetworkValid = NetWorkUtil.isNetWorkValidated(instance)
            val isConnected = viewModel.getConnectStates()
            val isWear = viewModel.getWearStates()
            Timber.d("checkStatus:$status, $isTranslate,$isNetworkValid,$isConnected,$isWear")
            withContext(Dispatchers.Main) {
                if (!isNetworkValid) {
                    requireContext().toast(R.string.record_error_network)
                    return@withContext
                }
                if (!isConnected || !isWear) {
                    requireContext().toast(R.string.text_alipay_connect_tips)
                    return@withContext
                }

                if (checkNeedShowUpdateDialog()) return@withContext

                if (status == ON_SITE_RECORDING || status == RECORD_VIDEO || isTranslate) {
                    val message = when (status) {
                        ON_SITE_RECORDING -> R.string.text_alipay_cannot_bind_while_recording
                        RECORD_VIDEO -> R.string.text_alipay_cannot_bind_while_vedio_recording
                        else -> R.string.text_alipay_cannot_bind_while_translating
                    }
                    requireContext().toast(message)
                    return@withContext
                }
                function.invoke()
            }
        }
    }

    private fun checkNeedShowUpdateDialog(): Boolean {
        if (!viewModel.isSupportAlipay()) {
            requireContext().toast(R.string.libs_please_update_device)
            showUpdateDialog()
            return true
        }
        return false
    }

    private fun showUpdateDialog() {
        viewModel.deviceStateLiveData?.value?.updateInfo?.let {
            Timber.d("showUpdateDialog: updateInfo $it")
            PriorityDialogManager.showDialog(
                ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment)
                    .apply {
                        arguments = bundleOf(
                            BundleKey.DeviceRoomUpdateInfo to it.convert(
                                viewModel.bondDevice?.model?.toInt() ?: 0
                            ),
                            BundleKey.DeviceUpdatePageFrom to getPageName()
                        )
                    },
                childFragmentManager,
                FRAGMENT_FIRM_TAG,
                DialogPriority.MEDIUM
            )
        } ?: run { Timber.d("showUpdateDialog: not found updateInfo") }
    }
}
